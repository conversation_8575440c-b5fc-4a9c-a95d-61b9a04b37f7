using UnityEngine;
using Fusion;

namespace SimpleFPS {
    public class HatController : NetworkBehaviour {
        [Header("Hat Settings")]
        public int hatId; // Hat ID

        [Header("Attachment Settings")]
        public Vector3 headOffset = new Vector3(0, 0.2f, 0); // Offset relative to head
        public Vector3 rotationOffset = Vector3.zero; // Rotation relative to head

        // Network fields for synchronization
        [Networked] public PlayerRef AttachedPlayerRef { get; set; }

        private PlayerController attachedPlayer;
        private Transform headTransform;

        public void AttachToPlayer(PlayerController player) {
            // Set network reference to player (only server can do this)
            if (Object.HasStateAuthority) {
                AttachedPlayerRef = player.Object.InputAuthority;
            }

            SetupAttachment(player);
        }

        private void SetupAttachment(PlayerController player) {
            attachedPlayer = player;

            // Find player's head (usually a child object named "Head" or similar)
            // If no special head object exists, use the player's transform
            headTransform = FindHeadTransform(player.transform);
            if (headTransform == null) {
                headTransform = player.transform;
            }

            // Attach hat to head
            transform.SetParent(headTransform);
            transform.localPosition = headOffset;
            transform.localRotation = Quaternion.Euler(rotationOffset);

            // Set hat layer to LocalPlayerBody
            SetLayerRecursively(gameObject, LayerMask.NameToLayer("LocalPlayerBody"));
        }

        private Transform FindHeadTransform(Transform playerTransform) {
            // Try to find head by name
            Transform head = playerTransform.Find("Head");
            if (head != null) return head;

            head = playerTransform.Find("head");
            if (head != null) return head;

            // Search in child objects
            foreach (Transform child in playerTransform.GetComponentsInChildren<Transform>()) {
                if (child.name.ToLower().Contains("head")) {
                    return child;
                }
            }

            return null;
        }

        private void SetLayerRecursively(GameObject obj, int layer) {
            obj.layer = layer;
            foreach (Transform child in obj.transform) {
                SetLayerRecursively(child.gameObject, layer);
            }
        }

        public override void Spawned() {
            // On spawn, try to find player by AttachedPlayerRef
            if (AttachedPlayerRef != default) {
                if (Runner.TryGetPlayerObject(AttachedPlayerRef, out var playerObj)) {
                    if (playerObj.TryGetComponent<PlayerController>(out var playerController)) {
                        SetupAttachment(playerController);
                    }
                }
            }
        }

        public override void FixedUpdateNetwork() {
            // If no attached player but AttachedPlayerRef exists, try to find player
            if (attachedPlayer == null && AttachedPlayerRef != default) {
                if (Runner.TryGetPlayerObject(AttachedPlayerRef, out var playerObj)) {
                    if (playerObj.TryGetComponent<PlayerController>(out var playerController)) {
                        SetupAttachment(playerController);
                    }
                }
            }

            // Ensure hat stays attached to player
            if (attachedPlayer != null && headTransform != null) {
                if (transform.parent != headTransform) {
                    transform.SetParent(headTransform);
                    transform.localPosition = headOffset;
                    transform.localRotation = Quaternion.Euler(rotationOffset);
                }
            }
        }

        public void DetachFromPlayer() {
            if (attachedPlayer != null) {
                attachedPlayer.CurrentHat = null;
                attachedPlayer = null;
            }

            // Clear network reference (only server can do this)
            if (Object.HasStateAuthority) {
                AttachedPlayerRef = default;
            }

            transform.SetParent(null);

            // Destroy hat
            if (Object != null && Object.IsValid) {
                Runner.Despawn(Object);
            }
        }
    }
}
