using Fusion;
using UnityEngine;
using SimpleFPS;
using System.Collections.Generic;

/// <summary>
/// King of the Hill game mode where players who are in the designated zone at the end of the match receive +200 money.
/// </summary>
public class KingOfTheHillGameMode : NetworkBehaviour, IGameModeConfig {
    public static KingOfTheHillGameMode Instance { get; private set; }

    #region Fields
    [Header("Gameplay Settings")]
    [SerializeField] private float lobbyDuration = 30f;
    [SerializeField] private float matchDuration = 120f;
    [SerializeField] private float playerRespawnTime = 5f;
    [SerializeField] private GameManager gameManager;

    [<PERSON><PERSON>("King of the Hill Settings")]
    [SerializeField] private int moneyReward = 200;
    [SerializeField] private KingOfTheHillTrigger hillTrigger;

    [Header("Mode Description")]
    [TextArea]
    [SerializeField] private string modeDescription = "King of the Hill: Players who are in the designated zone at the end of the match receive +200 money.";

    [Header("Spawn Points")]
    [SerializeField] private Transform loserSpawnPoint;

    // Список игроков, находящихся в зоне
    private readonly HashSet<PlayerRef> playersInZone = new HashSet<PlayerRef>();
    #endregion

    #region Properties
    public float LobbyDuration => lobbyDuration;
    public float MatchDuration => matchDuration;
    public float PlayerRespawnTime => playerRespawnTime;
    public Transform LoserSpawnPoint => loserSpawnPoint;
    public string ModeDescription => modeDescription;
    #endregion

    #region Unity Lifecycle
    private void Awake() {
        if (Instance == null) {
            Instance = this;
        }
        else if (Instance != this) {
            Destroy(gameObject);
        }
        
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    public override void Spawned() {
        // Только сервер выполняет игровую логику
        if (!Object.HasStateAuthority) {
            enabled = false;
            return;
        }

        if (Instance == null) {
            Instance = this;
        }

        // Найти триггер зоны если не назначен
        if (hillTrigger == null) {
            hillTrigger = FindObjectOfType<KingOfTheHillTrigger>();
        }
    }
    #endregion

    #region IGameModeConfig Implementation
    public void OnRoundStarted() {
        if (!Object.HasStateAuthority) {
            return;
        }

        // Очищаем список игроков в зоне при начале раунда
        playersInZone.Clear();
        
        if (hillTrigger != null) {
            hillTrigger.ClearZone();
        }
    }

    public void OnRoundEnded() {
        if (!Object.HasStateAuthority) {
            return;
        }

        // Награждаем игроков, находящихся в зоне
        AwardPlayersInZone();
    }

    public void DetermineWinnersAndLosers() {
        // В King of the Hill режиме нет явных победителей/проигравших
        // Все игроки остаются живыми, награда зависит от нахождения в зоне
    }

    public void ResetModeState() {
        playersInZone.Clear();
        
        if (hillTrigger != null) {
            hillTrigger.ClearZone();
        }
    }
    #endregion

    #region Zone Management
    public void RegisterPlayerInZone(PlayerRef playerRef) {
        if (!Object.HasStateAuthority) {
            return;
        }

        playersInZone.Add(playerRef);
    }

    public void UnregisterPlayerFromZone(PlayerRef playerRef) {
        if (!Object.HasStateAuthority) {
            return;
        }

        playersInZone.Remove(playerRef);
    }

    private void AwardPlayersInZone() {
        if (!Object.HasStateAuthority) {
            return;
        }

        // Награждаем всех игроков, находящихся в зоне на момент окончания матча
        foreach (var playerRef in playersInZone) {
            if (gameManager.PlayerData.TryGet(playerRef, out var playerData) && 
                playerData.IsConnected && playerData.IsAlive) {
                
                playerData.Money += moneyReward;
                gameManager.PlayerData.Set(playerRef, playerData);
            }
        }
    }

    // Метод для получения количества игроков в зоне (для UI или других целей)
    public int GetPlayersInZoneCount() {
        return playersInZone.Count;
    }

    // Метод для проверки, находится ли конкретный игрок в зоне
    public bool IsPlayerInZone(PlayerRef playerRef) {
        return playersInZone.Contains(playerRef);
    }
    #endregion
}
