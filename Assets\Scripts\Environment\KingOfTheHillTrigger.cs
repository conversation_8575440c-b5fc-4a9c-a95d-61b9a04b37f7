using Fusion;
using UnityEngine;
using SimpleFPS;
using System.Collections.Generic;

[RequireComponent(typeof(Collider))]
public class KingOfTheHillTrigger : NetworkBehaviour {

    [SerializeField] private GameManager gameManager;
    
    // Список игроков, находящихся в зоне
    private readonly HashSet<PlayerRef> playersInZone = new HashSet<PlayerRef>();

    private void Start() {
        if (gameManager == null) {
            gameManager = GameManager.Instance;
        }
    }

    private void OnTriggerEnter(Collider other) {
        if (Object == null || !Object.HasStateAuthority) {
            return;
        }

        var pc = other.GetComponentInParent<PlayerController>();
        if (pc == null) {
            return;
        }

        var pr = pc.Object.InputAuthority;

        if (gameManager == null || !gameManager.PlayerData.TryGet(pr, out var pd)) {
            return;
        }

        // Добавляем игрока в зону только если он жив
        if (pd.IsAlive) {
            playersInZone.Add(pr);
            
            if (KingOfTheHillGameMode.Instance != null) {
                KingOfTheHillGameMode.Instance.RegisterPlayerInZone(pr);
            }
        }
    }

    private void OnTriggerExit(Collider other) {
        if (Object == null || !Object.HasStateAuthority) {
            return;
        }

        var pc = other.GetComponentInParent<PlayerController>();
        if (pc == null) {
            return;
        }

        var pr = pc.Object.InputAuthority;

        // Удаляем игрока из зоны
        playersInZone.Remove(pr);
        
        if (KingOfTheHillGameMode.Instance != null) {
            KingOfTheHillGameMode.Instance.UnregisterPlayerFromZone(pr);
        }
    }

    // Метод для получения всех игроков в зоне (для использования в игровом режиме)
    public HashSet<PlayerRef> GetPlayersInZone() {
        return new HashSet<PlayerRef>(playersInZone);
    }

    // Очистка зоны при сбросе режима
    public void ClearZone() {
        playersInZone.Clear();
    }
}
