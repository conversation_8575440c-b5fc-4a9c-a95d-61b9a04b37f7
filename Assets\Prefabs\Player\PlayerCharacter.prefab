%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &13982166261312329
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 501319966028415895}
  - component: {fileID: 7362183419963793260}
  - component: {fileID: 5057136563194653715}
  - component: {fileID: 2277787515704380006}
  m_Layer: 0
  m_Name: CapFPP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &501319966028415895
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 13982166261312329}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.16394046}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4208488758885925859}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7362183419963793260
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 13982166261312329}
  m_Mesh: {fileID: 6239881831769578202, guid: 93f6e36168c0f0c42a1ed910e92efa49, type: 3}
--- !u!23 &5057136563194653715
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 13982166261312329}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9b0117405bd2f97499eec17626dd8e9e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &2277787515704380006
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 13982166261312329}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73175c4a97fa97540a5cb68258f497f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Speed: 180
  RotateX: 0
  RotateY: 1
  RotateZ: 0
--- !u!1 &14097553407493660
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4683132479299004705}
  m_Layer: 0
  m_Name: mixamorig:RightLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4683132479299004705
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 14097553407493660}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0702828, y: -0.0011674729, z: -0.02943302, w: 0.9970921}
  m_LocalPosition: {x: -0.000000007914901, y: 0.4059946, z: 0.0000000012136298}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1015713205207378585}
  - {fileID: 3051096137945382918}
  m_Father: {fileID: 2746651359175260108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &51832895571884886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 739477401244348312}
  - component: {fileID: 1451386081010592660}
  m_Layer: 0
  m_Name: Sifa
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &739477401244348312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 51832895571884886}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2685003817436795034}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1451386081010592660
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 51832895571884886}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 3
  itemVisual: {fileID: 3746436192610910442}
--- !u!1 &56201200016902789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8088835260423587755}
  m_Layer: 0
  m_Name: mixamorig:RightHandIndex4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8088835260423587755
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 56201200016902789}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.030779917, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4131724537613149640}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &57468322461064238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2242854680295213829}
  m_Layer: 0
  m_Name: shotgunBarrels
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2242854680295213829
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 57468322461064238}
  serializedVersion: 2
  m_LocalRotation: {x: 0.66399896, y: 0.000000025610149, z: 0.00000047614125, w: 0.74773353}
  m_LocalPosition: {x: 6.964456e-15, y: -0.00011094182, z: 0.0011043664}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2311131075923081171}
  m_Father: {fileID: 6113962709007682328}
  m_LocalEulerAnglesHint: {x: 83.211, y: 0, z: 0}
--- !u!1 &139719811184681368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8603261195543048622}
  - component: {fileID: 3197288615420238524}
  - component: {fileID: 1608120631956279764}
  m_Layer: 0
  m_Name: eye_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8603261195543048622
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 139719811184681368}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0.1, z: 0.2}
  m_LocalScale: {x: 0.1, y: 0.2, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6368492073700593264}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3197288615420238524
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 139719811184681368}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1608120631956279764
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 139719811184681368}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 089df2b5e9699af40bdcaad42291d420, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &169661297203159332
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5350666690279603229}
  m_Layer: 0
  m_Name: mixamorig:LeftUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5350666690279603229
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 169661297203159332}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000203363, y: -0.0063398825, z: 0.9999755, w: 0.0029697437}
  m_LocalPosition: {x: -0.091238745, y: -0.06657188, z: -0.0005540311}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5098982395316872754}
  - {fileID: 4439034502869381961}
  m_Father: {fileID: 2812900929563455671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &192025156330797507
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1563233221555756732}
  m_Layer: 0
  m_Name: shotgun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1563233221555756732
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192025156330797507}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3131700382896726967}
  m_Father: {fileID: 1477125319424636728}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &379520632447679509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8775210194547003685}
  m_Layer: 0
  m_Name: Bone.004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8775210194547003685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 379520632447679509}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000014123545, y: 0.7071068, z: -0.7071067, w: 0.0000014123543}
  m_LocalPosition: {x: 5.7149946e-18, y: -0.00003198683, z: -0.020802017}
  m_LocalScale: {x: 1, y: 0.9999997, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2835735554693225896}
  m_Father: {fileID: 3817182319982510986}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &409156983037069028
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1900327639670871439}
  m_Layer: 0
  m_Name: mixamorig:RightHandPinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1900327639670871439
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 409156983037069028}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000011118443, y: -0.000000026169207, z: 0.00000011052539, w: 1}
  m_LocalPosition: {x: -0.00000000422187, y: 0.04136643, z: -0.00000006338663}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4192918227577770735}
  m_Father: {fileID: 1904523340395724154}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &464180154004114753
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1890700611972516944}
  m_Layer: 0
  m_Name: mixamorig:RightToe_End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1890700611972516944
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 464180154004114753}
  serializedVersion: 2
  m_LocalRotation: {x: 1.0550137e-10, y: 0.000000021947926, z: -0.000000021100277, w: 1}
  m_LocalPosition: {x: 0.000000006966293, y: 0.09999603, z: 0.000000002235174}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6697979525565741582}
  m_Father: {fileID: 2764264087436889369}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &582982020560389706
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8443419981025829252}
  m_Layer: 0
  m_Name: mixamorig:RightHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8443419981025829252
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 582982020560389706}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000005373097, y: 0.000000114644905, z: -0.00000040291752, w: 1}
  m_LocalPosition: {x: 0.02216632, y: 0.12146999, z: -0.000100002166}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7946224408460213750}
  m_Father: {fileID: 3979363624826852168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &707787146469992759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8058059625419994929}
  - component: {fileID: 3275141421512189170}
  - component: {fileID: 5348872329891572733}
  m_Layer: 0
  m_Name: Circle.001 (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8058059625419994929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 707787146469992759}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06323266, y: 0.839115, z: 0.23122741, w: -0.48828438}
  m_LocalPosition: {x: -0.3301, y: 0.2438, z: -0.1984}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -26.731, y: -117.777, z: -7.702}
--- !u!33 &3275141421512189170
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 707787146469992759}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &5348872329891572733
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 707787146469992759}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &764615262392605446
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 991462354720707243}
  - component: {fileID: 2387298400630587860}
  m_Layer: 18
  m_Name: HitBox_LeftHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &991462354720707243
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 764615262392605446}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0.204, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 492023801351764627}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!114 &2387298400630587860
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 764615262392605446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.08, y: 0.08, z: 0.2}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 1
--- !u!1 &770092079893031907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8335864589201569616}
  m_Layer: 0
  m_Name: mixamorig:LeftHandThumb4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8335864589201569616
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 770092079893031907}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009146252, y: -0.12563756, z: -0.072072655, w: 0.9894125}
  m_LocalPosition: {x: 0.00000006318092, y: 0.03459077, z: 0.000000049511126}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9186690636105258064}
  m_Father: {fileID: 7642223773478067609}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &879441948947482831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3474128623356395765}
  - component: {fileID: 3173319630511133146}
  m_Layer: 0
  m_Name: PipeItem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3474128623356395765
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 879441948947482831}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5200410084863542775}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3173319630511133146
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 879441948947482831}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 11
  itemVisual: {fileID: 4854952724616634189}
--- !u!1 &916665279064959199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2002896310568615251}
  - component: {fileID: 2131800937902847223}
  m_Layer: 18
  m_Name: HitBox_Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2002896310568615251
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 916665279064959199}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8235076030647147179}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2131800937902847223
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 916665279064959199}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 2
  SphereRadius: 0.16
  CapsuleRadius: 0
  BoxExtents: {x: 0.2, y: 0.3, z: 0.18}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 2
--- !u!1 &939294754739483099
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3483370276486656611}
  - component: {fileID: 5347089725831786994}
  m_Layer: 0
  m_Name: Ticket
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3483370276486656611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 939294754739483099}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2749552526999073506}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5347089725831786994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 939294754739483099}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 10
  itemVisual: {fileID: 7418903900296664994}
--- !u!1 &952680951719134313
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7502871316924555518}
  - component: {fileID: 6715455323542387234}
  - component: {fileID: 8690295601283620168}
  m_Layer: 0
  m_Name: Mask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7502871316924555518
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 952680951719134313}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -0.000030925185, z: 0.00003078123, w: 0.7071067}
  m_LocalPosition: {x: 0.000095153744, y: -1.600753, z: 0.003407455}
  m_LocalScale: {x: 100.00001, y: 100.000015, z: 100.00001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8235076030647147179}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6715455323542387234
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 952680951719134313}
  m_Mesh: {fileID: 6690141603448424749, guid: 2334c99099786834783b0adde997dd4e, type: 3}
--- !u!23 &8690295601283620168
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 952680951719134313}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c61515962e1182242adfe56275a2c6b4, type: 2}
  - {fileID: 2100000, guid: 7a11963e61584164e95a03a190c0e07a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1036158987086278969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7562237312864672137}
  m_Layer: 0
  m_Name: mixamorig:LeftHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7562237312864672137
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036158987086278969}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000036765323, y: -0.000000262845, z: -0.00000008405006, w: 1}
  m_LocalPosition: {x: 0.000000004240592, y: 0.033073194, z: 0.00000003306375}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2706451453253038580}
  m_Father: {fileID: 5374398542902129178}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1045910784161519410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4898546221169652666}
  - component: {fileID: 7272850430872006958}
  m_Layer: 0
  m_Name: Cylinder.008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4898546221169652666
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045910784161519410}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5762509, y: -0.40979853, z: 0.40979856, w: 0.576251}
  m_LocalPosition: {x: 0.000000012417719, y: 0.0000000028562712, z: -0.006527146}
  m_LocalScale: {x: 100.00001, y: 69.56879, z: 100.00001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9046583445433809665}
  - {fileID: 3817182319982510986}
  m_Father: {fileID: 5555431556190608973}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7272850430872006958
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045910784161519410}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 00bce05200dc67440b34329ff07ce4df, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 5512361600001258384, guid: a045b2cad4cc6f049a2267c90edb222e, type: 3}
  m_Bones:
  - {fileID: 6758121775632514030}
  - {fileID: 3303035721439681923}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3303035721439681923}
  m_AABB:
    m_Center: {x: 2.3283064e-10, y: -9.313226e-10, z: 0.00011940097}
    m_Extent: {x: 0.004048148, y: 0.0040481496, z: 0.00093366957}
  m_DirtyAABB: 0
--- !u!1 &1078268617412761196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2142811172635914906}
  m_Layer: 0
  m_Name: mixamorig:LeftHandIndex4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2142811172635914906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1078268617412761196}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.030779876, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4554756828377643146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1136930432323698596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1015713205207378585}
  - component: {fileID: 6953547915529282632}
  m_Layer: 18
  m_Name: HitBox_RightLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1015713205207378585
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136930432323698596}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0702828, y: 0.0011674729, z: 0.02943302, w: 0.9970921}
  m_LocalPosition: {x: -0.01, y: 0.225, z: 0.04}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4683132479299004705}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6953547915529282632
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136930432323698596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.1, y: 0.2, z: 0.1}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 0.6
--- !u!1 &1216138359546406268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5555431556190608973}
  - component: {fileID: 4916325825169128541}
  m_Layer: 0
  m_Name: YoyoFPP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &5555431556190608973
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1216138359546406268}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.7071068, z: 0, w: 0.7071068}
  m_LocalPosition: {x: -0.2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4898546221169652666}
  - {fileID: 4643394305985780944}
  m_Father: {fileID: 2013405590072170886}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!95 &4916325825169128541
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1216138359546406268}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: a045b2cad4cc6f049a2267c90edb222e, type: 3}
  m_Controller: {fileID: 9100000, guid: d71e369ff4790b549a8f85dd78aa29f2, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1226132148245407448
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2812900929563455671}
  m_Layer: 0
  m_Name: mixamorig:Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2812900929563455671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1226132148245407448}
  serializedVersion: 2
  m_LocalRotation: {x: 7.537291e-11, y: 0.0000017531266, z: -0.000043883512, w: 1}
  m_LocalPosition: {x: 0.00000033016568, y: 0.99791867, z: 0.00000051621345}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5350666690279603229}
  - {fileID: 2746651359175260108}
  - {fileID: 451824914477906223}
  - {fileID: 8260150061676698042}
  m_Father: {fileID: 7888707815337306133}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1254700875416432076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8697762257909222889}
  m_Layer: 0
  m_Name: mixamorig:LeftHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8697762257909222889
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1254700875416432076}
  serializedVersion: 2
  m_LocalRotation: {x: 0.25189427, y: 0.05946438, z: -0.22192371, w: 0.9400868}
  m_LocalPosition: {x: 0.030029742, y: 0.03788828, z: 0.02167157}
  m_LocalScale: {x: 0.9999998, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2497612934451456764}
  m_Father: {fileID: 5419929674102356697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1433060238946553357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1808269780562582211}
  - component: {fileID: 540219735758708431}
  m_Layer: 0
  m_Name: Assyk
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1808269780562582211
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1433060238946553357}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2396075926389916231}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &540219735758708431
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1433060238946553357}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 6
  itemVisual: {fileID: 3029588775948146941}
--- !u!1 &1545607952670973836
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1969129186948030317}
  - component: {fileID: 417522658062499119}
  m_Layer: 0
  m_Name: Boombox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1969129186948030317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1545607952670973836}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1335128677452947767}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &417522658062499119
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1545607952670973836}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 7
  itemVisual: {fileID: 9056948391547330738}
--- !u!1 &1559029616497290695
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3875459241672687767}
  - component: {fileID: 3613516075423982096}
  - component: {fileID: 5185482994654817825}
  m_Layer: 0
  m_Name: Circle.001 (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3875459241672687767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1559029616497290695}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5118059, y: -0.23168424, z: 0.3908335, w: -0.7291271}
  m_LocalPosition: {x: 0.3324, y: 0.2393, z: 0.1993}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -34.419, y: 63.448, z: -78.064}
--- !u!33 &3613516075423982096
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1559029616497290695}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &5185482994654817825
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1559029616497290695}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1568950199242880161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8856331593397087652}
  m_Layer: 0
  m_Name: mixamorig:Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8856331593397087652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1568950199242880161}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00019613648, y: -3.183217e-12, z: 0.000009316163, w: 1}
  m_LocalPosition: {x: -1.5115801e-11, y: 0.11731982, z: -0.000000006975916}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3945256988625133481}
  m_Father: {fileID: 451824914477906223}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1669851827122620826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3402534984475402420}
  - component: {fileID: 1056747090640485175}
  m_Layer: 0
  m_Name: shotgunMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3402534984475402420
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669851827122620826}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8809163776382285054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1056747090640485175
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669851827122620826}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5f07c625847716642a1d2cda26395467, type: 2}
  - {fileID: 2100000, guid: 6247e7591a187944cac07b47d75e3be3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -2578597674188618612, guid: 0faba8f2d76279e40b4e279bf82aa54c, type: 3}
  m_Bones:
  - {fileID: 6113962709007682328}
  - {fileID: 2242854680295213829}
  - {fileID: 1184128333235670976}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6113962709007682328}
  m_AABB:
    m_Center: {x: 5.820766e-11, y: -0.0004417888, z: 0.00087714294}
    m_Extent: {x: 0.00034913432, y: 0.001031038, z: 0.0021444745}
  m_DirtyAABB: 0
--- !u!1 &1786839536415043500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5006849877934591033}
  m_Layer: 0
  m_Name: mixamorig:RightHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5006849877934591033
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1786839536415043500}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000010195647, y: 0.00000016996896, z: 0.0000006109476, w: 1}
  m_LocalPosition: {x: -0.000000039339064, y: 0.047449645, z: -0.00000022061809}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3823537968796596275}
  m_Father: {fileID: 8816832650939081125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1794162179648238565
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4305824487323871446}
  - component: {fileID: 8175773810173180838}
  - component: {fileID: 2757435449578730600}
  m_Layer: 6
  m_Name: PlayerName
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4305824487323871446
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794162179648238565}
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1281982887216609419}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 3.81}
  m_SizeDelta: {x: 5, y: 5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!23 &8175773810173180838
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794162179648238565}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &2757435449578730600
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794162179648238565}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Sample text
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 1
  m_fontSizeBase: 1
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 3.6759882, z: 0, w: 0.29167914}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 8175773810173180838}
  m_maskType: 0
--- !u!1 &1822079213608060363
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3540884956922506229}
  m_Layer: 0
  m_Name: mixamorig:RightForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3540884956922506229
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822079213608060363}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000019492713, y: -0.000000093990124, z: -0.000000026580542, w: 1}
  m_LocalPosition: {x: -0.0000000011241355, y: 0.27404663, z: -8.357824e-10}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3979363624826852168}
  m_Father: {fileID: 3015082048921422199}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1842893108087106039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1107263349141600685}
  - component: {fileID: 8911821726155121995}
  - component: {fileID: 9082998013031723920}
  m_Layer: 0
  m_Name: shells
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1107263349141600685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1842893108087106039}
  serializedVersion: 2
  m_LocalRotation: {x: 0.91597563, y: -0.12059048, z: -0.0499502, w: -0.37940952}
  m_LocalPosition: {x: -0.00021914659, y: 0.085669585, z: 0.068817504}
  m_LocalScale: {x: 0.80000013, y: 0.80000013, z: 0.80000013}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1477125319424636728}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8911821726155121995
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1842893108087106039}
  m_Mesh: {fileID: 3297411833427862167, guid: 0faba8f2d76279e40b4e279bf82aa54c, type: 3}
--- !u!23 &9082998013031723920
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1842893108087106039}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6ce9dc103f4107b45ad1a866ce0e197f, type: 2}
  - {fileID: 2100000, guid: 14c2d7e277d4f9d4590795b86bd982be, type: 2}
  - {fileID: 2100000, guid: 83c24d5547e3e9f4382e262a4b8781bf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1906168965651213121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1904523340395724154}
  m_Layer: 0
  m_Name: mixamorig:RightHandPinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1904523340395724154
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1906168965651213121}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000001914701, y: 0.0000001374635, z: -0.0000003782502, w: 1}
  m_LocalPosition: {x: 0.047258336, y: 0.109081894, z: 0.0022612887}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1900327639670871439}
  m_Father: {fileID: 3979363624826852168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1909900111695880753
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1281982887216609419}
  - component: {fileID: 5128096326010086129}
  m_Layer: 0
  m_Name: ThirdPersonRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1281982887216609419
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909900111695880753}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4305824487323871446}
  - {fileID: 7888707815337306133}
  - {fileID: 3866336657270361032}
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &5128096326010086129
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909900111695880753}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 2334c99099786834783b0adde997dd4e, type: 3}
  m_Controller: {fileID: 9100000, guid: 726dbc3ac369cdc40bb700f64cfc54ca, type: 2}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1915580073463247477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7271550270425546183}
  - component: {fileID: 7321165444117198797}
  - component: {fileID: 1148709042425809546}
  m_Layer: 0
  m_Name: Circle.001 (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7271550270425546183
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1915580073463247477}
  serializedVersion: 2
  m_LocalRotation: {x: -0.42223278, y: 0.26823094, z: -0.29880756, w: -0.8127027}
  m_LocalPosition: {x: -0.1275, y: -0.377, z: 0.2213}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: 57.844, y: -20.185, z: 29.14}
--- !u!33 &7321165444117198797
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1915580073463247477}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &1148709042425809546
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1915580073463247477}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1946153009545347936
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8256387542372928475}
  m_Layer: 0
  m_Name: Bone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8256387542372928475
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1946153009545347936}
  serializedVersion: 2
  m_LocalRotation: {x: -0.46484935, y: 0.5328368, z: 0.5857793, w: -0.39605898}
  m_LocalPosition: {x: 1.0960174e-18, y: -0.000031987736, z: 0}
  m_LocalScale: {x: 0.42698547, y: 0.42698553, z: 0.42698547}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5662694111764699321}
  m_Father: {fileID: 3817182319982510986}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1977614393171767913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6697979525565741582}
  m_Layer: 0
  m_Name: mixamorig:RightToe_End_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6697979525565741582
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1977614393171767913}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.09999605, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1890700611972516944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2013553399449382212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7133777301257100208}
  m_Layer: 0
  m_Name: mixamorig:RightHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7133777301257100208
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013553399449382212}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000001462359, y: -0.00000008170355, z: 0.000000010463539, w: 1}
  m_LocalPosition: {x: 0.0000000012959844, y: 0.03891972, z: 0.000000020407763}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1196209538880856154}
  m_Father: {fileID: 5486752880079072316}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2066442507550953120
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1539293413828028038}
  - component: {fileID: 2843217098204823132}
  - component: {fileID: 8471241586654702665}
  m_Layer: 0
  m_Name: Circle.001 (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1539293413828028038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066442507550953120}
  serializedVersion: 2
  m_LocalRotation: {x: -0.08345506, y: 0.49664247, z: 0.15768908, w: -0.84942085}
  m_LocalPosition: {x: -0.4094, y: 0.0153, z: 0.2097}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -0.851, y: -60.474, z: -20.538}
--- !u!33 &2843217098204823132
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066442507550953120}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &8471241586654702665
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2066442507550953120}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2080775277735648260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 447037791068707182}
  m_Layer: 0
  m_Name: FirstPersonItemHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &447037791068707182
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2080775277735648260}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.2, y: -0.21978033, z: 0.56394047}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6182511211354245451}
  - {fileID: 739477401244348312}
  - {fileID: 8328102573727064540}
  - {fileID: 6008282678656264706}
  - {fileID: 1808269780562582211}
  - {fileID: 1969129186948030317}
  - {fileID: 4208488758885925859}
  - {fileID: 2013405590072170886}
  - {fileID: 3483370276486656611}
  - {fileID: 3474128623356395765}
  - {fileID: 3597526475082932922}
  - {fileID: 2016852394519663927}
  - {fileID: 7243968780005644107}
  - {fileID: 4054486398653610702}
  - {fileID: 6705765931282195924}
  m_Father: {fileID: 5953457501803415890}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2082424259777554406
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 473561278133575726}
  - component: {fileID: 2045895116347512710}
  - component: {fileID: 6692217000355520893}
  m_Layer: 0
  m_Name: Circle.001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &473561278133575726
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2082424259777554406}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6302983, y: -0.5884505, z: 0.36666948, w: -0.3492901}
  m_LocalPosition: {x: 0.4, y: 0, z: -0.213}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -0.503, y: 119.151, z: -93.638}
--- !u!33 &2045895116347512710
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2082424259777554406}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &6692217000355520893
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2082424259777554406}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2137332429774125349
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7245041755106022299}
  m_Layer: 0
  m_Name: mixamorig:LeftHandPinky4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7245041755106022299
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137332429774125349}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000002859943, y: -0.004099586, z: 0.0005753814, w: 0.9999914}
  m_LocalPosition: {x: -0.0000000015243676, y: 0.029238814, z: 0.00000009585461}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5422958465231124983}
  m_Father: {fileID: 5377745395822255802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2170654983669995102
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5912451314015105652}
  m_Layer: 0
  m_Name: mixamorig:RightHandMiddle4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5912451314015105652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2170654983669995102}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.036801897, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4775554780989781846}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2305107505980451479
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3710916564237207501}
  - component: {fileID: 5109834541994214569}
  - component: {fileID: 3257107749858803449}
  m_Layer: 0
  m_Name: LaserVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3710916564237207501
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2305107505980451479}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: 0.7071068, w: -0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: -0.14}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4521568328032124891}
  - {fileID: 5164038286161047702}
  m_Father: {fileID: 6008282678656264706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 270}
--- !u!33 &5109834541994214569
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2305107505980451479}
  m_Mesh: {fileID: 1181821589465821284, guid: 1bca8c940e907ce47950866507f2b605, type: 3}
--- !u!23 &3257107749858803449
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2305107505980451479}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1a8cd9edab6000949a87a8ac93d0f3c6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2330239629956452123
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8235076030647147179}
  m_Layer: 0
  m_Name: mixamorig:Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8235076030647147179
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2330239629956452123}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000006061822, y: -1.4676437e-13, z: -1.4552153e-11, w: 1}
  m_LocalPosition: {x: -0.000000025611497, y: 0.103218384, z: 0.031424295}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6368492073700593264}
  - {fileID: 2002896310568615251}
  - {fileID: 7502871316924555518}
  - {fileID: 5012347389853430900}
  m_Father: {fileID: 7536925568937949801}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2377694222328777868
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8794285573910322956}
  m_Layer: 0
  m_Name: Bone.001_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8794285573910322956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2377694222328777868}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.003606337, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3303035721439681923}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2394337813539577321
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4337974688800858457}
  - component: {fileID: 69942759630723570}
  - component: {fileID: 2221047180235739790}
  m_Layer: 0
  m_Name: mouth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4337974688800858457
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2394337813539577321}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.132, z: 0.634}
  m_LocalScale: {x: 0.5, y: 0.1, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6368492073700593264}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &69942759630723570
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2394337813539577321}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2221047180235739790
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2394337813539577321}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3efb6bd9355a63e419ef2954494d3af6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2407424229145772791
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8462894559293616617}
  m_Layer: 0
  m_Name: mixamorig:RightHandRing4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8462894559293616617
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2407424229145772791}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000060653507, y: 0.0076133455, z: 0.00030036215, w: 0.999971}
  m_LocalPosition: {x: -0.00000023582018, y: 0.036601245, z: 0.0000000070092705}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2933040711103675446}
  m_Father: {fileID: 6021729432587539947}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2408087050009236722
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3303035721439681923}
  m_Layer: 0
  m_Name: Bone.001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3303035721439681923
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2408087050009236722}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8794285573910322956}
  m_Father: {fileID: 4643394305985780944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2520168088621051660
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5419929674102356697}
  m_Layer: 0
  m_Name: mixamorig:LeftHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5419929674102356697
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2520168088621051660}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000076627566, y: -0.00000011445312, z: -0.000000054842936, w: 1}
  m_LocalPosition: {x: 0.0000000056747376, y: 0.27614456, z: -0.00000010005}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4781822877642270152}
  - {fileID: 1819887180558210830}
  - {fileID: ************1408069}
  - {fileID: 5525986193531111571}
  - {fileID: 8697762257909222889}
  m_Father: {fileID: 1365167465497023489}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2520875887650864842
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1468892336596193987}
  - component: {fileID: 3558741946176311797}
  - component: {fileID: 1292607783949039701}
  m_Layer: 19
  m_Name: Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1468892336596193987
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2520875887650864842}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6705765931282195924}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3558741946176311797
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2520875887650864842}
  m_Mesh: {fileID: -2432090755550338912, guid: 32eb2f28606e789478bf93bfaedc9d1d, type: 3}
--- !u!23 &1292607783949039701
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2520875887650864842}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: fb7eacb64f2d83c4b8c3156e52c752ad, type: 2}
  - {fileID: 2100000, guid: c37ed78a05302384fb95761bc252f358, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2537828430714445819
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1819887180558210830}
  m_Layer: 0
  m_Name: mixamorig:LeftHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1819887180558210830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2537828430714445819}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000014902271, y: -0.00000008757008, z: -0.00000037281603, w: 1}
  m_LocalPosition: {x: 0.00000019207619, y: 0.1277554, z: 0.000000003741186}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2619256717875300951}
  m_Father: {fileID: 5419929674102356697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2552101500549285750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1637557678266448222}
  - component: {fileID: 3776939712830924763}
  - component: {fileID: 4152655241411856876}
  m_Layer: 0
  m_Name: Circle.001 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1637557678266448222
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2552101500549285750}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5275178, y: 0.020599555, z: -0.13083458, w: -0.83915615}
  m_LocalPosition: {x: -0.1124, y: 0.3913, z: 0.202}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -61.636, y: -21.305, z: 30.528}
--- !u!33 &3776939712830924763
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2552101500549285750}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &4152655241411856876
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2552101500549285750}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2562569579127700067
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7105427196862503063}
  m_Layer: 0
  m_Name: shotgunTrigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7105427196862503063
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2562569579127700067}
  serializedVersion: 2
  m_LocalRotation: {x: 0.97572595, y: -0.000000026106243, z: -0.0000001163156, w: 0.21899515}
  m_LocalPosition: {x: 4.3242964e-20, y: -0.00019474901, z: 0.000014791062}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 138908218874710332}
  m_Father: {fileID: 3131700382896726967}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2655860485571729739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7861715622442640672}
  m_Layer: 0
  m_Name: mixamorig:RightHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7861715622442640672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2655860485571729739}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000056852735, y: 0.0000001427203, z: 0.000000023508855, w: 1}
  m_LocalPosition: {x: 0.0000002872393, y: 0.03459767, z: -0.000000029362216}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4775554780989781846}
  m_Father: {fileID: 7639872423608008687}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2712265799052510964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1739786462606985517}
  - component: {fileID: 5916202116017335851}
  m_Layer: 0
  m_Name: DeathSound
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1739786462606985517
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2712265799052510964}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.60850257, y: 1.1750021, z: -0.08200241}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &5916202116017335851
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2712265799052510964}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 0.1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 50
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &2726899323313174715
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3084967074228336637}
  - component: {fileID: 1288948062804153847}
  - component: {fileID: 3713134115177563683}
  m_Layer: 0
  m_Name: Circle.001 (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3084967074228336637
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2726899323313174715}
  serializedVersion: 2
  m_LocalRotation: {x: -0.3830243, y: 0.7455526, z: -0.059628658, w: -0.54211456}
  m_LocalPosition: {x: -0.3288, y: -0.2345, z: -0.2104}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: 30.278, y: -117.976, z: -35.908}
--- !u!33 &1288948062804153847
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2726899323313174715}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &3713134115177563683
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2726899323313174715}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2728151611422556326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4521568328032124891}
  - component: {fileID: 6831470760382601658}
  - component: {fileID: 4841991043589739370}
  - component: {fileID: 3447446528939375760}
  - component: {fileID: 2643738400833175917}
  m_Layer: 0
  m_Name: LaserBeamFX1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &4521568328032124891
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728151611422556326}
  serializedVersion: 2
  m_LocalRotation: {x: 0.07749845, y: -0.009657337, z: 0.0007507218, w: 0.9969454}
  m_LocalPosition: {x: 0, y: 0, z: -0.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3710916564237207501}
  m_LocalEulerAnglesHint: {x: 8.89, y: -1.11, z: 0}
--- !u!2083052967 &6831470760382601658
VisualEffect:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728151611422556326}
  m_Enabled: 1
  m_Asset: {fileID: 8926484042661614526, guid: 19aa61fead9dd584587e30f72238a51a, type: 3}
  m_InitialEventName: OnPlay
  m_InitialEventNameOverriden: 0
  m_StartSeed: 0
  m_ResetSeedOnPlay: 0
  m_AllowInstancing: 1
  m_ResourceVersion: 1
  m_PropertySheet:
    m_Float:
      m_Array:
      - m_Value: 0.2
        m_Name: Duration
        m_Overridden: 1
      - m_Value: 0.263
        m_Name: ElectricBeamDistortionAmount
        m_Overridden: 0
      - m_Value: 0.96
        m_Name: ElectricBeamAlphaClip
        m_Overridden: 0
      - m_Value: 0
        m_Name: FlashSize
        m_Overridden: 1
      - m_Value: 1
        m_Name: ElectricFlashSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: DarkFlashSize
        m_Overridden: 1
      - m_Value: 1
        m_Name: InitialParticlesSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactRingRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactFlashSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactFlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactRingSize
        m_Overridden: 1
      - m_Value: 2
        m_Name: BeamCoreSize
        m_Overridden: 1
      - m_Value: 2
        m_Name: FlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ParticlesRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactFlashLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: ElectricFlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: DarkFlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: FlashLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: DarkFlashLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: BeamCoreAlphaClip
        m_Overridden: 0
      - m_Value: 30
        m_Name: BeamCoreDistortionScale
        m_Overridden: 1
      - m_Value: 1
        m_Name: ImpactDelay
        m_Overridden: 0
      - m_Value: 1
        m_Name: InitialParticlesRate
        m_Overridden: 1
      - m_Value: 1
        m_Name: InitialParticlesLifetime
        m_Overridden: 1
      - m_Value: 0.2
        m_Name: ElectricFlashLifetime
        m_Overridden: 0
      - m_Value: 0
        m_Name: ImpactParticlesRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactParticlesLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactParticlesSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactRingLifetime
        m_Overridden: 1
    m_Vector2f:
      m_Array:
      - m_Value: {x: 2, y: 2}
        m_Name: ImpactRingsFlipbookSize
        m_Overridden: 1
      - m_Value: {x: -1.5, y: 0}
        m_Name: ElectricBeamMainTexSpeed
        m_Overridden: 0
      - m_Value: {x: -1.5, y: 0}
        m_Name: DarkBeamMainTexSpeed
        m_Overridden: 0
      - m_Value: {x: 1, y: 0}
        m_Name: BeamCoreMainTexSpeed
        m_Overridden: 1
    m_Vector3f:
      m_Array:
      - m_Value: {x: 0, y: 0, z: 270}
        m_Name: BeamEndPoint_angles
        m_Overridden: 1
      - m_Value: {x: 0.2, y: 1.4588537, z: 10.42394}
        m_Name: BeamEndPoint_position
        m_Overridden: 1
      - m_Value: {x: 1, y: 1, z: 1}
        m_Name: BeamEndPoint_scale
        m_Overridden: 1
      - m_Value: {x: 2, y: 2, z: 2}
        m_Name: ElectricBeamScale
        m_Overridden: 1
      - m_Value: {x: 1, y: 0.05, z: 1}
        m_Name: DarkBeamScale
        m_Overridden: 1
      - m_Value: {x: 1, y: 1, z: 1}
        m_Name: BeamCoreScale
        m_Overridden: 1
      - m_Value: {x: 2, y: 2, z: 2}
        m_Name: BeamsScale
        m_Overridden: 1
      - m_Value: {x: 1, y: 1, z: 1}
        m_Name: InitialParticlesScale
        m_Overridden: 1
      - m_Value: {x: 0.15, y: 1, z: 1}
        m_Name: ImpactParticlesScale
        m_Overridden: 1
    m_Vector4f:
      m_Array:
      - m_Value: {x: 32.59885, y: 0, z: 1.0500048, w: 1}
        m_Name: ElectricBeamColor
        m_Overridden: 1
      - m_Value: {x: 0.5597484, y: 0, z: 0.017769722, w: 1}
        m_Name: BeamCoreColor
        m_Overridden: 1
      - m_Value: {x: 33.88229, y: 33.88229, z: 33.88229, w: 1}
        m_Name: ParticlesColor
        m_Overridden: 1
      - m_Value: {x: 1, y: 0, z: 0, w: 1}
        m_Name: DarkBeamColor
        m_Overridden: 1
    m_Uint:
      m_Array: []
    m_Int:
      m_Array: []
    m_Matrix4x4f:
      m_Array: []
    m_AnimationCurve:
      m_Array:
      - m_Value:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 1.4921834
            value: 0.018644975
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 2.4921834
            value: 0.018644975
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Name: BeamsWidthCurve
        m_Overridden: 1
      - m_Value:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: -0.091962874
            value: 1.3010803
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.9080371
            value: 1.3010803
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Name: BeamsDepthCurve
        m_Overridden: 1
    m_Gradient:
      m_Array: []
    m_NamedObject:
      m_Array:
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ImpactFlashTexture
        m_Overridden: 0
      - m_Value: {fileID: 2800000, guid: 0dbeee96a2cc7534087b899e9edf5847, type: 3}
        m_Name: ImpactRingTexture
        m_Overridden: 1
      - m_Value: {fileID: 2800000, guid: 27aa58f2adbfe234c8e5e055e418c4ec, type: 3}
        m_Name: BeamCoreTexture
        m_Overridden: 0
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: FlashTexture
        m_Overridden: 1
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ElectricFlashTexture
        m_Overridden: 0
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ParticlesTexture
        m_Overridden: 1
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: InitialParticlesTexture
        m_Overridden: 1
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: DarkFlashTexture
        m_Overridden: 0
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ImpactParticlesTextures
        m_Overridden: 1
    m_Bool:
      m_Array: []
--- !u!73398921 &4841991043589739370
VFXRenderer:
  serializedVersion: 1
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728151611422556326}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!114 &3447446528939375760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728151611422556326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdafc37f32b176349b1684c4455b98e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ExecuteInEditor: 1
  m_Bindings:
  - {fileID: 2643738400833175917}
  m_VisualEffect: {fileID: 6831470760382601658}
--- !u!114 &2643738400833175917
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728151611422556326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7fdfea1f838247c40921a07afedde962, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Property:
    m_Name: BeamEndPoint
  Target: {fileID: 0}
--- !u!1 &2760270992753925068
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6477283916791895790}
  m_Layer: 0
  m_Name: Bone.005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6477283916791895790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2760270992753925068}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000011302407, y: -0.34822702, z: -0.0000000031654162, w: 0.93741024}
  m_LocalPosition: {x: 2.6382637e-12, y: 0.0050540934, z: 2.1727338e-12}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2356688143703533270}
  m_Father: {fileID: 1999178560297187524}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2835019204085012708
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7502453955486831123}
  m_Layer: 0
  m_Name: shotgunTrigger_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7502453955486831123
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2835019204085012708}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.00019612582, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1184128333235670976}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2958823821205566864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 40247463152598488}
  - component: {fileID: 8564069321059057408}
  - component: {fileID: 4191206976539643123}
  m_Layer: 0
  m_Name: DuckVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &40247463152598488
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2958823821205566864}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: -0.5}
  m_LocalPosition: {x: 0, y: 0, z: -0.15382668}
  m_LocalScale: {x: 0.6, y: 0.6, z: 0.6}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3597526475082932922}
  m_LocalEulerAnglesHint: {x: 270, y: 0, z: 90}
--- !u!23 &8564069321059057408
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2958823821205566864}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 70d7c341a3adc2f4ba3de6a9afde4390, type: 2}
  - {fileID: 2100000, guid: 089df2b5e9699af40bdcaad42291d420, type: 2}
  - {fileID: 2100000, guid: 3efb6bd9355a63e419ef2954494d3af6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &4191206976539643123
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2958823821205566864}
  m_Mesh: {fileID: -5495902117074765545, guid: 78141d2de80ab0344acfd3cfb2cd2592, type: 3}
--- !u!1 &3002464726365836275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9186690636105258064}
  m_Layer: 0
  m_Name: mixamorig:LeftHandThumb4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9186690636105258064
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3002464726365836275}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.034590766, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8335864589201569616}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3029588775948146941
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2396075926389916231}
  - component: {fileID: 1652620717672959458}
  - component: {fileID: 3534127791770040616}
  m_Layer: 0
  m_Name: AssykFPP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2396075926389916231
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3029588775948146941}
  serializedVersion: 2
  m_LocalRotation: {x: -0.6200174, y: -0.5796855, z: -0.3071868, w: 0.43032482}
  m_LocalPosition: {x: 0.0036076158, y: 0.033204943, z: -0.19082746}
  m_LocalScale: {x: 1.5, y: 1.5, z: 1.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1808269780562582211}
  m_LocalEulerAnglesHint: {x: -117.157, y: 14.981003, z: -95.333984}
--- !u!33 &1652620717672959458
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3029588775948146941}
  m_Mesh: {fileID: 2822760135350616294, guid: c96ff41d2fde6964b9b7a1e67e5a90f4, type: 3}
--- !u!23 &3534127791770040616
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3029588775948146941}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b41e68184bdb24d40a94b24a9aa496b9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3063832048695666219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7904184815933716786}
  m_Layer: 0
  m_Name: ThirdPersonItemHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7904184815933716786
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3063832048695666219}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000024577477, y: -0.00000020479638, z: -0.000000027974977, w: 1}
  m_LocalPosition: {x: 0, y: 0.1, z: 0.05}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3979363624826852168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3253527039183895951
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5374398542902129178}
  m_Layer: 0
  m_Name: mixamorig:LeftHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5374398542902129178
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3253527039183895951}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000088244883, y: 0.000000032395324, z: 0.00000012037226, w: 1}
  m_LocalPosition: {x: -0.0000000012692272, y: 0.036011804, z: 0.000000085566185}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7562237312864672137}
  m_Father: {fileID: 5525986193531111571}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3284744376334236802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7999865686142132600}
  m_Layer: 0
  m_Name: mixamorig:LeftFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7999865686142132600
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3284744376334236802}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5832494, y: -0.028363103, z: -0.04264953, w: 0.8106767}
  m_LocalPosition: {x: -3.72529e-10, y: 0.43157348, z: 0.0000000041443853}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4795485296645381212}
  m_Father: {fileID: 4439034502869381961}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3405667388726187820
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4439034502869381961}
  m_Layer: 0
  m_Name: mixamorig:LeftLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4439034502869381961
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3405667388726187820}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07027033, y: 0.001166511, z: 0.029433167, w: 0.99709296}
  m_LocalPosition: {x: 0.0000000044393245, y: 0.40599433, z: -2.5553162e-10}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5556556886925936216}
  - {fileID: 7999865686142132600}
  m_Father: {fileID: 5350666690279603229}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3417968666225378946
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5072942232072495766}
  m_Layer: 6
  m_Name: CameraHandle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5072942232072495766
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3417968666225378946}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.678634, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5953457501803415890}
  - {fileID: 7085806987371351361}
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3481336041963478719
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6589849949144326397}
  m_Layer: 0
  m_Name: mixamorig:RightHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6589849949144326397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3481336041963478719}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000003204942, y: 0.000000019841035, z: 0.00000051922814, w: 1}
  m_LocalPosition: {x: -0.00000017415208, y: 0.12775527, z: 0.000000100769576}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7639872423608008687}
  m_Father: {fileID: 3979363624826852168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3600571855620098530
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1999178560297187524}
  m_Layer: 0
  m_Name: Bone.002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1999178560297187524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3600571855620098530}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5603637, y: -0.43126848, z: 0.43126848, w: 0.5603637}
  m_LocalPosition: {x: 1.0960174e-18, y: -0.000031987736, z: 0}
  m_LocalScale: {x: 0.42698553, y: 0.42698556, z: 0.42698553}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6477283916791895790}
  m_Father: {fileID: 3817182319982510986}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3601223108846194486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4781822877642270152}
  m_Layer: 0
  m_Name: mixamorig:LeftHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4781822877642270152
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3601223108846194486}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000016409024, y: -0.00000011652904, z: 0.00000023723828, w: 1}
  m_LocalPosition: {x: 0.028220445, y: 0.12266633, z: 0.0023182905}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1380415997891719551}
  m_Father: {fileID: 5419929674102356697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3612264991280350060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3866336657270361032}
  - component: {fileID: 8396210338827519755}
  m_Layer: 0
  m_Name: HockeyPlayerMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3866336657270361032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3612264991280350060}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1281982887216609419}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8396210338827519755
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3612264991280350060}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 91ac68598b03bae46a0457c184e7e2a6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 6604927694719916108, guid: 2334c99099786834783b0adde997dd4e, type: 3}
  m_Bones:
  - {fileID: 2812900929563455671}
  - {fileID: 451824914477906223}
  - {fileID: 8856331593397087652}
  - {fileID: 3945256988625133481}
  - {fileID: 7536925568937949801}
  - {fileID: 8235076030647147179}
  - {fileID: 5012347389853430900}
  - {fileID: 2693815886802083000}
  - {fileID: 492023801351764627}
  - {fileID: 1365167465497023489}
  - {fileID: 5419929674102356697}
  - {fileID: 8697762257909222889}
  - {fileID: 2497612934451456764}
  - {fileID: 7642223773478067609}
  - {fileID: 8335864589201569616}
  - {fileID: 4781822877642270152}
  - {fileID: 1380415997891719551}
  - {fileID: 3975215770376766830}
  - {fileID: 4554756828377643146}
  - {fileID: 1819887180558210830}
  - {fileID: 2619256717875300951}
  - {fileID: 2665138742993220774}
  - {fileID: 2269580635770912648}
  - {fileID: 5525986193531111571}
  - {fileID: 5374398542902129178}
  - {fileID: 7562237312864672137}
  - {fileID: 2706451453253038580}
  - {fileID: ************1408069}
  - {fileID: 993087210239133413}
  - {fileID: 5377745395822255802}
  - {fileID: 7245041755106022299}
  - {fileID: 4929543403243503095}
  - {fileID: 3015082048921422199}
  - {fileID: 3540884956922506229}
  - {fileID: 3979363624826852168}
  - {fileID: 8816832650939081125}
  - {fileID: 5006849877934591033}
  - {fileID: 3823537968796596275}
  - {fileID: 7632165182453806318}
  - {fileID: 5486752880079072316}
  - {fileID: 7133777301257100208}
  - {fileID: 1196209538880856154}
  - {fileID: 4131724537613149640}
  - {fileID: 6589849949144326397}
  - {fileID: 7639872423608008687}
  - {fileID: 7861715622442640672}
  - {fileID: 4775554780989781846}
  - {fileID: 8443419981025829252}
  - {fileID: 7946224408460213750}
  - {fileID: 6021729432587539947}
  - {fileID: 8462894559293616617}
  - {fileID: 1904523340395724154}
  - {fileID: 1900327639670871439}
  - {fileID: 4192918227577770735}
  - {fileID: 1963095064706926255}
  - {fileID: 5350666690279603229}
  - {fileID: 4439034502869381961}
  - {fileID: 7999865686142132600}
  - {fileID: 4795485296645381212}
  - {fileID: 4236546463364950899}
  - {fileID: 2746651359175260108}
  - {fileID: 4683132479299004705}
  - {fileID: 3051096137945382918}
  - {fileID: 2764264087436889369}
  - {fileID: 1890700611972516944}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2812900929563455671}
  m_AABB:
    m_Center: {x: -0.000031739473, y: -0.15285218, z: 0.0034961477}
    m_Extent: {x: 0.9618273, y: 0.9917425, z: 0.21994859}
  m_DirtyAABB: 0
--- !u!1 &3630597467832297876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2013405590072170886}
  - component: {fileID: 6341926714964820258}
  m_Layer: 0
  m_Name: Yoyo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2013405590072170886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3630597467832297876}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5555431556190608973}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6341926714964820258
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3630597467832297876}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 9
  itemVisual: {fileID: 1216138359546406268}
--- !u!1 &3648053270336279509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4236546463364950899}
  m_Layer: 0
  m_Name: mixamorig:LeftToe_End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4236546463364950899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3648053270336279509}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000039195584, y: -0.00000004962203, z: 0.00000004525646, w: 1}
  m_LocalPosition: {x: -0.0000000035017729, y: 0.099995986, z: 0.000000001490116}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7113456155712932134}
  m_Father: {fileID: 4795485296645381212}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3692451107446907298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 138908218874710332}
  m_Layer: 0
  m_Name: shotgunTrigger_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &138908218874710332
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3692451107446907298}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.00019612582, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7105427196862503063}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3698632270704035265
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5556556886925936216}
  - component: {fileID: 7623785809873158776}
  m_Layer: 18
  m_Name: HitBox_LeftLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5556556886925936216
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3698632270704035265}
  serializedVersion: 2
  m_LocalRotation: {x: 0.07027034, y: -0.001166511, z: -0.029433168, w: 0.99709296}
  m_LocalPosition: {x: 0.01, y: 0.23, z: 0.041}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4439034502869381961}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7623785809873158776
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3698632270704035265}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.1, y: 0.2, z: 0.1}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 0.6
--- !u!1 &3746436192610910442
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2685003817436795034}
  - component: {fileID: 2314043707573881732}
  - component: {fileID: 4102186403294909195}
  m_Layer: 0
  m_Name: Sifa_Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2685003817436795034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3746436192610910442}
  serializedVersion: 2
  m_LocalRotation: {x: -0.92387956, y: 0, z: 0, w: 0.38268343}
  m_LocalPosition: {x: 0, y: 0.03, z: -0.2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 739477401244348312}
  m_LocalEulerAnglesHint: {x: -135, y: 0, z: 0}
--- !u!33 &2314043707573881732
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3746436192610910442}
  m_Mesh: {fileID: -2580947126951671879, guid: 09f1523265233da41847e73f017d4ea1, type: 3}
--- !u!23 &4102186403294909195
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3746436192610910442}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: db7b49305feebe149873ff5438f19c58, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3748827240111302516
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1477125319424636728}
  m_Layer: 0
  m_Name: ShotgunVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1477125319424636728
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3748827240111302516}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.9838165, z: 0.1791791, w: 0}
  m_LocalPosition: {x: -0.209, y: -0.033761263, z: 0.009943545}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1107263349141600685}
  - {fileID: 1563233221555756732}
  - {fileID: 7666835147254385239}
  - {fileID: 3597362949140177810}
  m_Father: {fileID: 7243968780005644107}
  m_LocalEulerAnglesHint: {x: -20.644, y: 180, z: 0}
--- !u!1 &3767043493583740451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6758121775632514030}
  m_Layer: 0
  m_Name: Bone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6758121775632514030
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3767043493583740451}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6201429966234266367}
  m_Father: {fileID: 4643394305985780944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3768537040152676684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1306324168190396528}
  m_Layer: 0
  m_Name: Bone.007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1306324168190396528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3768537040152676684}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7047729, y: 0.05740417, z: -0.057404067, w: 0.70477283}
  m_LocalPosition: {x: 0.0021495891, y: -0.000031988715, z: 0.019702205}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7817572369376365240}
  m_Father: {fileID: 3817182319982510986}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3779084686377733377
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5012347389853430900}
  m_Layer: 0
  m_Name: mixamorig:HeadTop_End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5012347389853430900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3779084686377733377}
  serializedVersion: 2
  m_LocalRotation: {x: 8.370041e-17, y: 1.6746912e-13, z: 3.8011e-16, w: 1}
  m_LocalPosition: {x: -0.000001545164, y: 0.1847467, z: 0.066364005}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4001656304030456512}
  m_Father: {fileID: 8235076030647147179}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3786472929159043228
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6705765931282195924}
  - component: {fileID: 3328079944747579122}
  m_Layer: 0
  m_Name: Beer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6705765931282195924
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3786472929159043228}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1468892336596193987}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3328079944747579122
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3786472929159043228}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 17
  itemVisual: {fileID: 2520875887650864842}
--- !u!1 &3793508246530004336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2311131075923081171}
  m_Layer: 0
  m_Name: shotgunBarrels_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2311131075923081171
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3793508246530004336}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0019475808, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2242854680295213829}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3799689957916831353
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6201429966234266367}
  m_Layer: 0
  m_Name: Bone_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6201429966234266367
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3799689957916831353}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0034442812, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6758121775632514030}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3829508050869827009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2619256717875300951}
  m_Layer: 0
  m_Name: mixamorig:LeftHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2619256717875300951
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3829508050869827009}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000028882152, y: 0.000000056664433, z: 0.00000011626876, w: 1}
  m_LocalPosition: {x: 0.0000000034102412, y: 0.036139768, z: 0.000000064656156}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2665138742993220774}
  m_Father: {fileID: 1819887180558210830}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3865239346182737882
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7085806987371351361}
  m_Layer: 6
  m_Name: LookAtPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7085806987371351361
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3865239346182737882}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 3.248}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5072942232072495766}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3912974017175077659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 15628321936966587}
  - component: {fileID: 1910258529398982482}
  m_Layer: 6
  m_Name: VoiceNetworkObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &15628321936966587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3912974017175077659}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.71, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1910258529398982482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3912974017175077659}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6376dd7fc23b3764483a7349b58502e2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &3938795972483521802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4554756828377643146}
  m_Layer: 0
  m_Name: mixamorig:LeftHandIndex4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4554756828377643146
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3938795972483521802}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000002387751, y: -0.0040015313, z: -0.000022705288, w: 0.999992}
  m_LocalPosition: {x: 0.0000000018081846, y: 0.030780055, z: -0.000000010235617}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2142811172635914906}
  m_Father: {fileID: 3975215770376766830}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4034109323235034539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1196209538880856154}
  m_Layer: 0
  m_Name: mixamorig:RightHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1196209538880856154
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4034109323235034539}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000001525438, y: 0.000000001949363, z: 0.00000010625854, w: 1}
  m_LocalPosition: {x: 0.0000000013599856, y: 0.034151662, z: -0.00000006272344}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4131724537613149640}
  m_Father: {fileID: 7133777301257100208}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4039314331603385070
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8260150061676698042}
  - component: {fileID: 4064681035785975638}
  m_Layer: 18
  m_Name: HitBox_Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8260150061676698042
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4039314331603385070}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000037673573, y: -0.0000017531249, z: 0.000043883512, w: 1}
  m_LocalPosition: {x: -0.0000196146, y: 0.22108132, z: -0.034000427}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2812900929563455671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4064681035785975638
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4039314331603385070}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.2, y: 0.33, z: 0.18}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 1
--- !u!1 &4199784822379677035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6368492073700593264}
  - component: {fileID: 6987123147147039187}
  - component: {fileID: 8090654476832039959}
  m_Layer: 0
  m_Name: DuckMask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &6368492073700593264
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4199784822379677035}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000046108692, y: -0.00000010179145, z: 0.00004363302, w: 1}
  m_LocalPosition: {x: -0, y: 0.154, z: 0.027}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.29999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8603261195543048622}
  - {fileID: 4326703914208961461}
  - {fileID: 4337974688800858457}
  m_Father: {fileID: 8235076030647147179}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6987123147147039187
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4199784822379677035}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8090654476832039959
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4199784822379677035}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 70d7c341a3adc2f4ba3de6a9afde4390, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4228172950969008670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3828028002783979645}
  m_Layer: 0
  m_Name: mixamorig:RightHandThumb4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3828028002783979645
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4228172950969008670}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.03459073, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7632165182453806318}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4257192811888698403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2497612934451456764}
  m_Layer: 0
  m_Name: mixamorig:LeftHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2497612934451456764
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4257192811888698403}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000041738696, y: -0.0000002797068, z: 0.000000063329935, w: 1}
  m_LocalPosition: {x: 0.000000009536743, y: 0.047449667, z: 0.00000013169048}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7642223773478067609}
  m_Father: {fileID: 8697762257909222889}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4260200998989714414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5953457501803415890}
  m_Layer: 6
  m_Name: FirstPersonRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5953457501803415890
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4260200998989714414}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6250869745270454292}
  - {fileID: 447037791068707182}
  m_Father: {fileID: 5072942232072495766}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4380222955465766942
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5098982395316872754}
  - component: {fileID: 1780460002125149266}
  m_Layer: 18
  m_Name: HitBox_LeftUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5098982395316872754
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380222955465766942}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.001, y: 0.237, z: 0.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5350666690279603229}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1780460002125149266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380222955465766942}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.1, y: 0.2, z: 0.1}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 0.6
--- !u!1 &4383909923422644011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2835735554693225896}
  m_Layer: 0
  m_Name: Bone.004_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2835735554693225896
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4383909923422644011}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.006589515, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775210194547003685}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4409040098955899531
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2665138742993220774}
  m_Layer: 0
  m_Name: mixamorig:LeftHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2665138742993220774
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4409040098955899531}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000005685273, y: -0.00000011508212, z: 0.000000004127587, w: 1}
  m_LocalPosition: {x: -0.00000027788064, y: 0.034597736, z: 0.000000036294395}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2269580635770912648}
  m_Father: {fileID: 2619256717875300951}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4421753481863940593
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3975215770376766830}
  m_Layer: 0
  m_Name: mixamorig:LeftHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3975215770376766830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4421753481863940593}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000026258856, y: -0.000000009227742, z: -0.00000011353576, w: 1}
  m_LocalPosition: {x: 0.0000000012094824, y: 0.03415172, z: -0.00000013773004}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4554756828377643146}
  m_Father: {fileID: 1380415997891719551}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4554418069008487192
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5377745395822255802}
  m_Layer: 0
  m_Name: mixamorig:LeftHandPinky3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5377745395822255802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4554418069008487192}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000017438547, y: -0.0000005590284, z: -0.0000008450494, w: 1}
  m_LocalPosition: {x: 7.205381e-10, y: 0.025948253, z: -0.00000019722223}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7245041755106022299}
  m_Father: {fileID: 993087210239133413}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4616785223918100701
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6113962709007682328}
  m_Layer: 0
  m_Name: shotgunRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6113962709007682328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4616785223918100701}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2242854680295213829}
  - {fileID: 1184128333235670976}
  m_Father: {fileID: 7628779179626297188}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4854952724616634189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5200410084863542775}
  - component: {fileID: 9188551594871458386}
  - component: {fileID: 6368298471052115608}
  m_Layer: 0
  m_Name: Pipe
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &5200410084863542775
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4854952724616634189}
  serializedVersion: 2
  m_LocalRotation: {x: 0.37773412, y: -0, z: -0, w: 0.92591417}
  m_LocalPosition: {x: 0, y: 0.05129649, z: -0.127}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3474128623356395765}
  m_LocalEulerAnglesHint: {x: 44.387, y: 0, z: 0}
--- !u!33 &9188551594871458386
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4854952724616634189}
  m_Mesh: {fileID: 880713509674552115, guid: c010dfc7eecd84f4c83872e4a73f1fe2, type: 3}
--- !u!23 &6368298471052115608
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4854952724616634189}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c578f7f21ca5461418f216bb7b01851b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4883458493418937657
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7628779179626297188}
  m_Layer: 0
  m_Name: shotgun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7628779179626297188
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4883458493418937657}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6113962709007682328}
  m_Father: {fileID: 8809163776382285054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4975913151499933980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5640246200841618342}
  - component: {fileID: 8784822598675876867}
  - component: {fileID: 6777076131921574089}
  m_Layer: 0
  m_Name: PocketKnifeFPP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &5640246200841618342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4975913151499933980}
  serializedVersion: 2
  m_LocalRotation: {x: -0.92387956, y: 0, z: 0, w: 0.38268343}
  m_LocalPosition: {x: 0, y: 0.03, z: -0.2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8328102573727064540}
  m_LocalEulerAnglesHint: {x: -135, y: 0, z: 0}
--- !u!33 &8784822598675876867
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4975913151499933980}
  m_Mesh: {fileID: -8663802791670341782, guid: 281dc3bae13372c4180d24cc53654bf9, type: 3}
--- !u!23 &6777076131921574089
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4975913151499933980}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: de8b316ea7f02e34da5e804343e72e80, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5117152354837485893
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2016852394519663927}
  - component: {fileID: 8273326723281441724}
  m_Layer: 0
  m_Name: Ball
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2016852394519663927
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5117152354837485893}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2451077743497916356}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8273326723281441724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5117152354837485893}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 14
  itemVisual: {fileID: 6509347645386774457}
--- !u!1 &5182658501963792037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5164038286161047702}
  - component: {fileID: 2767901023285366190}
  - component: {fileID: 5127875369737037513}
  - component: {fileID: 243097220123437658}
  - component: {fileID: 3606380845121673251}
  m_Layer: 0
  m_Name: LaserBeamFX2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &5164038286161047702
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5182658501963792037}
  serializedVersion: 2
  m_LocalRotation: {x: 0.07749845, y: -0.009657337, z: 0.0007507218, w: 0.9969454}
  m_LocalPosition: {x: 0, y: 0, z: -0.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3710916564237207501}
  m_LocalEulerAnglesHint: {x: 8.89, y: -1.11, z: 0}
--- !u!2083052967 &2767901023285366190
VisualEffect:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5182658501963792037}
  m_Enabled: 1
  m_Asset: {fileID: 8926484042661614526, guid: 19aa61fead9dd584587e30f72238a51a, type: 3}
  m_InitialEventName: OnPlay
  m_InitialEventNameOverriden: 0
  m_StartSeed: 0
  m_ResetSeedOnPlay: 0
  m_AllowInstancing: 1
  m_ResourceVersion: 1
  m_PropertySheet:
    m_Float:
      m_Array:
      - m_Value: 0.2
        m_Name: Duration
        m_Overridden: 1
      - m_Value: 0.263
        m_Name: ElectricBeamDistortionAmount
        m_Overridden: 0
      - m_Value: 0.96
        m_Name: ElectricBeamAlphaClip
        m_Overridden: 0
      - m_Value: 0
        m_Name: FlashSize
        m_Overridden: 1
      - m_Value: 1
        m_Name: ElectricFlashSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: DarkFlashSize
        m_Overridden: 1
      - m_Value: 1
        m_Name: InitialParticlesSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactRingRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactFlashSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactFlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactRingSize
        m_Overridden: 1
      - m_Value: 2
        m_Name: BeamCoreSize
        m_Overridden: 1
      - m_Value: 2
        m_Name: FlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ParticlesRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactFlashLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: ElectricFlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: DarkFlashRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: FlashLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: DarkFlashLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: BeamCoreAlphaClip
        m_Overridden: 0
      - m_Value: 30
        m_Name: BeamCoreDistortionScale
        m_Overridden: 1
      - m_Value: 1
        m_Name: ImpactDelay
        m_Overridden: 0
      - m_Value: 1
        m_Name: InitialParticlesRate
        m_Overridden: 1
      - m_Value: 1
        m_Name: InitialParticlesLifetime
        m_Overridden: 1
      - m_Value: 0.2
        m_Name: ElectricFlashLifetime
        m_Overridden: 0
      - m_Value: 0
        m_Name: ImpactParticlesRate
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactParticlesLifetime
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactParticlesSize
        m_Overridden: 1
      - m_Value: 0
        m_Name: ImpactRingLifetime
        m_Overridden: 1
    m_Vector2f:
      m_Array:
      - m_Value: {x: 2, y: 2}
        m_Name: ImpactRingsFlipbookSize
        m_Overridden: 1
      - m_Value: {x: -1.5, y: 0}
        m_Name: ElectricBeamMainTexSpeed
        m_Overridden: 0
      - m_Value: {x: -1.5, y: 0}
        m_Name: DarkBeamMainTexSpeed
        m_Overridden: 0
      - m_Value: {x: 1, y: 0}
        m_Name: BeamCoreMainTexSpeed
        m_Overridden: 1
    m_Vector3f:
      m_Array:
      - m_Value: {x: 0, y: 0, z: 270}
        m_Name: BeamEndPoint_angles
        m_Overridden: 1
      - m_Value: {x: 0.2, y: 1.4588537, z: 10.42394}
        m_Name: BeamEndPoint_position
        m_Overridden: 1
      - m_Value: {x: 1, y: 1, z: 1}
        m_Name: BeamEndPoint_scale
        m_Overridden: 1
      - m_Value: {x: 2, y: 2, z: 2}
        m_Name: ElectricBeamScale
        m_Overridden: 1
      - m_Value: {x: 1, y: 0.05, z: 1}
        m_Name: DarkBeamScale
        m_Overridden: 1
      - m_Value: {x: 1, y: 1, z: 1}
        m_Name: BeamCoreScale
        m_Overridden: 1
      - m_Value: {x: 2, y: 2, z: 2}
        m_Name: BeamsScale
        m_Overridden: 1
      - m_Value: {x: 1, y: 1, z: 1}
        m_Name: InitialParticlesScale
        m_Overridden: 1
      - m_Value: {x: 0.15, y: 1, z: 1}
        m_Name: ImpactParticlesScale
        m_Overridden: 1
    m_Vector4f:
      m_Array:
      - m_Value: {x: 6.1831446, y: 32.598858, z: 0, w: 1}
        m_Name: ElectricBeamColor
        m_Overridden: 1
      - m_Value: {x: 0, y: 0.56078434, z: 0.026900187, w: 1}
        m_Name: BeamCoreColor
        m_Overridden: 1
      - m_Value: {x: 33.88229, y: 33.88229, z: 33.88229, w: 1}
        m_Name: ParticlesColor
        m_Overridden: 1
      - m_Value: {x: 0.061195254, y: 1, z: 0, w: 1}
        m_Name: DarkBeamColor
        m_Overridden: 1
    m_Uint:
      m_Array: []
    m_Int:
      m_Array: []
    m_Matrix4x4f:
      m_Array: []
    m_AnimationCurve:
      m_Array:
      - m_Value:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 1.4921834
            value: 0.018644975
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 2.4921834
            value: 0.018644975
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Name: BeamsWidthCurve
        m_Overridden: 1
      - m_Value:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: -0.091962874
            value: 1.3010803
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.9080371
            value: 1.3010803
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Name: BeamsDepthCurve
        m_Overridden: 1
    m_Gradient:
      m_Array: []
    m_NamedObject:
      m_Array:
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ImpactFlashTexture
        m_Overridden: 0
      - m_Value: {fileID: 2800000, guid: 0dbeee96a2cc7534087b899e9edf5847, type: 3}
        m_Name: ImpactRingTexture
        m_Overridden: 1
      - m_Value: {fileID: 2800000, guid: 27aa58f2adbfe234c8e5e055e418c4ec, type: 3}
        m_Name: BeamCoreTexture
        m_Overridden: 0
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: FlashTexture
        m_Overridden: 1
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ElectricFlashTexture
        m_Overridden: 0
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ParticlesTexture
        m_Overridden: 1
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: InitialParticlesTexture
        m_Overridden: 1
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: DarkFlashTexture
        m_Overridden: 0
      - m_Value: {fileID: 10300, guid: 0000000000000000f000000000000000, type: 0}
        m_Name: ImpactParticlesTextures
        m_Overridden: 1
    m_Bool:
      m_Array: []
--- !u!73398921 &5127875369737037513
VFXRenderer:
  serializedVersion: 1
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5182658501963792037}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!114 &243097220123437658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5182658501963792037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdafc37f32b176349b1684c4455b98e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ExecuteInEditor: 1
  m_Bindings:
  - {fileID: 3606380845121673251}
  m_VisualEffect: {fileID: 2767901023285366190}
--- !u!114 &3606380845121673251
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5182658501963792037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7fdfea1f838247c40921a07afedde962, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Property:
    m_Name: BeamEndPoint
  Target: {fileID: 0}
--- !u!1 &5183261167122818833
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2269580635770912648}
  m_Layer: 0
  m_Name: mixamorig:LeftHandMiddle4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2269580635770912648
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5183261167122818833}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000011506883, y: -0.0045793834, z: 0.0006541389, w: 0.99998933}
  m_LocalPosition: {x: 0.00000031248345, y: 0.036801994, z: 0.000000056553727}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2900324770955326982}
  m_Father: {fileID: 2665138742993220774}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5190334451607957161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4001656304030456512}
  m_Layer: 0
  m_Name: mixamorig:HeadTop_End_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4001656304030456512
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5190334451607957161}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.19630462, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5012347389853430900}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5296429408625835626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7075321060331311280}
  m_Layer: 0
  m_Name: mixamorig:LeftHandRing4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7075321060331311280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5296429408625835626}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.03660107, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2706451453253038580}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5352887062010292027
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3131700382896726967}
  m_Layer: 0
  m_Name: shotgunRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3131700382896726967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5352887062010292027}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 386286488635967096}
  - {fileID: 7105427196862503063}
  m_Father: {fileID: 1563233221555756732}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5454705192447841324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 451824914477906223}
  m_Layer: 0
  m_Name: mixamorig:Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &451824914477906223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5454705192447841324}
  serializedVersion: 2
  m_LocalRotation: {x: -0.060730226, y: -0.000000022983519, z: -0.0000058700098, w: 0.9981542}
  m_LocalPosition: {x: -0.000008597851, y: 0.09923462, z: -0.012273348}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8856331593397087652}
  m_Father: {fileID: 2812900929563455671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5509031174905583066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1365167465497023489}
  m_Layer: 0
  m_Name: mixamorig:LeftForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1365167465497023489
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5509031174905583066}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000005901277, y: 0.000000066465056, z: 0.000000007684, w: 1}
  m_LocalPosition: {x: -0.000000002745437, y: 0.2740468, z: -0.00000009736139}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5419929674102356697}
  m_Father: {fileID: 492023801351764627}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5543435761671484673
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6021729432587539947}
  m_Layer: 0
  m_Name: mixamorig:RightHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6021729432587539947
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5543435761671484673}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000003342023, y: -0.000000068188356, z: -0.00000023209077, w: 1}
  m_LocalPosition: {x: 0.0000000049320352, y: 0.033073165, z: 0.0000000626132}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8462894559293616617}
  m_Father: {fileID: 7946224408460213750}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5557309268738864980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2706451453253038580}
  m_Layer: 0
  m_Name: mixamorig:LeftHandRing4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2706451453253038580
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5557309268738864980}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000889172, y: -0.0073961774, z: -0.0007705603, w: 0.99997234}
  m_LocalPosition: {x: 0.00000023709748, y: 0.036601253, z: 0.00000013841792}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7075321060331311280}
  m_Father: {fileID: 7562237312864672137}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5598356431250035342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5525986193531111571}
  m_Layer: 0
  m_Name: mixamorig:LeftHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5525986193531111571
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5598356431250035342}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000015881205, y: -0.00000018216309, z: -0.0000004576877, w: 1}
  m_LocalPosition: {x: -0.022166299, y: 0.12147019, z: -0.00009994389}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5374398542902129178}
  m_Father: {fileID: 5419929674102356697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5659471315953631692
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9046583445433809665}
  - component: {fileID: 3820603847680376145}
  m_Layer: 0
  m_Name: BezierCurve
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9046583445433809665
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5659471315953631692}
  serializedVersion: 2
  m_LocalRotation: {x: -0.42994925, y: -0.5613766, z: -0.42994922, w: 0.5613766}
  m_LocalPosition: {x: -0.2484642, y: -0.067060135, z: -9.313256e-10}
  m_LocalScale: {x: 183.8587, y: 183.85869, z: 183.85869}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4898546221169652666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &3820603847680376145
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5659471315953631692}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 00bce05200dc67440b34329ff07ce4df, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 150581138598605843, guid: a045b2cad4cc6f049a2267c90edb222e, type: 3}
  m_Bones:
  - {fileID: 8256387542372928475}
  - {fileID: 5662694111764699321}
  - {fileID: 1232300103819153417}
  - {fileID: 1306324168190396528}
  - {fileID: 1999178560297187524}
  - {fileID: 6477283916791895790}
  - {fileID: 2356688143703533270}
  - {fileID: 8775210194547003685}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1999178560297187524}
  m_AABB:
    m_Center: {x: -0.0002062528, y: 0.002777366, z: -0.0021706107}
    m_Extent: {x: 0.005004512, y: 0.064101964, z: 0.010402687}
  m_DirtyAABB: 0
--- !u!1 &5663504159410386316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2069568807697715976}
  m_Layer: 0
  m_Name: shotgunBarrels_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2069568807697715976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5663504159410386316}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0019475808, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 386286488635967096}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5683942932949259326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2746651359175260108}
  m_Layer: 0
  m_Name: mixamorig:RightUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2746651359175260108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5683942932949259326}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00001826125, y: -0.006333346, z: 0.99997526, w: -0.0030572577}
  m_LocalPosition: {x: 0.091250315, y: -0.06655602, z: -0.00055352744}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3450794738422352007}
  - {fileID: 4683132479299004705}
  m_Father: {fileID: 2812900929563455671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5708201157838273282
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3450794738422352007}
  - component: {fileID: 2186094072346708344}
  m_Layer: 18
  m_Name: HitBox_RightUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3450794738422352007
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5708201157838273282}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.001, y: 0.239, z: 0.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2746651359175260108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2186094072346708344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5708201157838273282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.1, y: 0.2, z: 0.1}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 0.6
--- !u!1 &5864983795468441617
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7869059498303244213}
  - component: {fileID: 369768546009484114}
  - component: {fileID: 6578277655137388256}
  m_Layer: 0
  m_Name: Circle.001 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7869059498303244213
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5864983795468441617}
  serializedVersion: 2
  m_LocalRotation: {x: 0.84228736, y: -0.10374465, z: 0.1396656, w: -0.51017904}
  m_LocalPosition: {x: 0.129, y: 0.384, z: -0.205}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -56.146, y: 142.24, z: -145.283}
--- !u!33 &369768546009484114
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5864983795468441617}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &6578277655137388256
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5864983795468441617}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5947756535587460870
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2141234006331023539}
  m_Layer: 0
  m_Name: Football
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2141234006331023539
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5947756535587460870}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2481288568660384567}
  m_Father: {fileID: 2451077743497916356}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6052205562745127265
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4054486398653610702}
  - component: {fileID: 6890996192398849050}
  m_Layer: 0
  m_Name: Gun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4054486398653610702
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6052205562745127265}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8809163776382285054}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6890996192398849050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6052205562745127265}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 16
  itemVisual: {fileID: 9201071923094070232}
--- !u!1 &6119781382727258074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5628067510458253910}
  - component: {fileID: 5048575707741812899}
  - component: {fileID: 646443308888812871}
  m_Layer: 0
  m_Name: Circle.001 (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5628067510458253910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6119781382727258074}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0029463083, y: 0.9986862, z: 0.029851405, w: -0.041547474}
  m_LocalPosition: {x: -0.0104, y: 0.0014, z: -0.4574}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: -3.404, y: -175.221, z: -0.48}
--- !u!33 &5048575707741812899
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6119781382727258074}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &646443308888812871
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6119781382727258074}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6155085180234684349
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4947442548220141526}
  - component: {fileID: 9132808568954676308}
  - component: {fileID: 8665802273229149104}
  - component: {fileID: 117776711380776047}
  m_Layer: 0
  m_Name: cm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4947442548220141526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6155085180234684349}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.02, y: -0.00863409, z: 0.1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6250869745270454292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9132808568954676308
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6155085180234684349}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac0b09e7857660247b1477e93731de29, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8665802273229149104
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6155085180234684349}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 36d1163fa822e8b418a0a603ec078d5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Damping: 0
--- !u!114 &117776711380776047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6155085180234684349}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e8b78ac948f05a46a6d8339a503172b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6167592135297318127
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8090084788032799967}
  - component: {fileID: 8950084132834479254}
  m_Layer: 18
  m_Name: HitBox_RightHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8090084788032799967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6167592135297318127}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0.214, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3015082048921422199}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!114 &8950084132834479254
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6167592135297318127}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d92c48adf59f19043827e3ecdfead691, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: 1
  SphereRadius: 0
  CapsuleRadius: 0
  BoxExtents: {x: 0.08, y: 0.08, z: 0.2}
  CapsuleExtents: 0
  Offset: {x: 0, y: 0, z: 0}
  Root: {fileID: 4839163721224306418}
  GizmosColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  DamageMultiplier: 1
--- !u!1 &6183860391103523166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1232300103819153417}
  m_Layer: 0
  m_Name: Bone.001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1232300103819153417
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6183860391103523166}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000028474012, y: 0.0000000048633386, z: -0.019851236, w: 0.99980295}
  m_LocalPosition: {x: 4.1909514e-11, y: 0.0050751334, z: -1.5587495e-11}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3218305483167469604}
  m_Father: {fileID: 5662694111764699321}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6229950523159141790
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3218305483167469604}
  m_Layer: 0
  m_Name: Bone.001_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3218305483167469604
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6229950523159141790}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0055978876, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1232300103819153417}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6320895401663794892
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4694018579658672886}
  - component: {fileID: 3171541257830873253}
  - component: {fileID: 748430302951710362}
  m_Layer: 0
  m_Name: Circle.001 (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4694018579658672886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6320895401663794892}
  serializedVersion: 2
  m_LocalRotation: {x: -0.19623235, y: -0.43678892, z: 0.1521597, w: -0.8646131}
  m_LocalPosition: {x: 0.327, y: -0.238, z: 0.2179}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: 28.181, y: 52.104, z: -5.971}
--- !u!33 &3171541257830873253
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6320895401663794892}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &748430302951710362
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6320895401663794892}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6325509175030969636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2764264087436889369}
  m_Layer: 0
  m_Name: mixamorig:RightToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2764264087436889369
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6325509175030969636}
  serializedVersion: 2
  m_LocalRotation: {x: 0.22765128, y: -0.03187858, z: -0.011131654, w: 0.9731571}
  m_LocalPosition: {x: 0.000000007152557, y: 0.15719794, z: -0.0000000077486035}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1890700611972516944}
  m_Father: {fileID: 3051096137945382918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6504476235764627034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5422958465231124983}
  m_Layer: 0
  m_Name: mixamorig:LeftHandPinky4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5422958465231124983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6504476235764627034}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.029238528, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7245041755106022299}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6509347645386774457
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2451077743497916356}
  m_Layer: 0
  m_Name: BallPickSO
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2451077743497916356
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6509347645386774457}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2141234006331023539}
  m_Father: {fileID: 2016852394519663927}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6591596489966982037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7397464212288208869}
  - component: {fileID: 9195169381146776712}
  m_Layer: 6
  m_Name: FootstepSound
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7397464212288208869
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6591596489966982037}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.573}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &9195169381146776712
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6591596489966982037}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: -973724649278464913, guid: bfbc8b07bd216478eb5fca35a6fe51e2, type: 2}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 0.17
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 3
  MaxDistance: 50
  Pan2D: 0
  rolloffMode: 2
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0.03
      value: 1
      inSlope: -33.346664
      outSlope: -33.346664
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.06
      value: 0.46783447
      inSlope: -8.336666
      outSlope: -8.336666
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.12
      value: 0.21783447
      inSlope: -2.0841665
      outSlope: -2.0841665
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.24
      value: 0.09283447
      inSlope: -0.52104163
      outSlope: -0.52104163
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.48
      value: 0.030334473
      inSlope: -0.13026041
      outSlope: -0.13026041
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.96
      value: 0
      inSlope: -0.032565102
      outSlope: -0.032565102
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: -0.030011963
      outSlope: -0.030011963
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.41666666
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &6612049089525044946
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3015082048921422199}
  m_Layer: 0
  m_Name: mixamorig:RightArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3015082048921422199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6612049089525044946}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010435795, y: -0.0010612794, z: 0.10114989, w: 0.9948159}
  m_LocalPosition: {x: -0.00000000834465, y: 0.12922288, z: 0.00000018577384}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8090084788032799967}
  - {fileID: 3540884956922506229}
  m_Father: {fileID: 4929543403243503095}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6644663815046717973
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6626120662396188838}
  - component: {fileID: 3008441207636892366}
  - component: {fileID: 4239483589914281303}
  m_Layer: 0
  m_Name: Circle.001 (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6626120662396188838
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6644663815046717973}
  serializedVersion: 2
  m_LocalRotation: {x: -0.04346334, y: 0.05724261, z: -0.09476284, w: -0.992902}
  m_LocalPosition: {x: 0.0042, y: -0.005, z: 0.4529}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: 5.576, y: -6.081, z: 10.607}
--- !u!33 &3008441207636892366
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6644663815046717973}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &4239483589914281303
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6644663815046717973}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6685117426942176090
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6250869745270454292}
  - component: {fileID: 6830563917783714441}
  m_Layer: 6
  m_Name: FirstPersonCamera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6250869745270454292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6685117426942176090}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4947442548220141526}
  m_Father: {fileID: 5953457501803415890}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6830563917783714441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6685117426942176090}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45e653bab7fb20e499bda25e1b646fea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ExcludedPropertiesInInspector:
  - m_Script
  m_LockStageInInspector: 
  m_StreamingVersion: 20170927
  m_Priority: 10
  m_StandbyUpdate: 2
  m_LookAt: {fileID: 7085806987371351361}
  m_Follow: {fileID: 5072942232072495766}
  m_Lens:
    FieldOfView: 60
    OrthographicSize: 10
    NearClipPlane: 0.05
    FarClipPlane: 1000
    Dutch: 0
    ModeOverride: 0
    LensShift: {x: 0, y: 0}
    GateFit: 2
    FocusDistance: 10
    m_SensorSize: {x: 1, y: 1}
  m_Transitions:
    m_BlendHint: 0
    m_InheritPosition: 0
    m_OnCameraLive:
      m_PersistentCalls:
        m_Calls: []
  m_LegacyBlendHint: 0
  m_ComponentOwner: {fileID: 4947442548220141526}
--- !u!1 &6695448191159465930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: ************1408069}
  m_Layer: 0
  m_Name: mixamorig:LeftHandPinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &************1408069
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6695448191159465930}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000001382815, y: -0.00000025545546, z: 0.00000019991023, w: 1}
  m_LocalPosition: {x: -0.047258317, y: 0.10908209, z: 0.0022613571}
  m_LocalScale: {x: 0.9999998, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 993087210239133413}
  m_Father: {fileID: 5419929674102356697}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6696435169527005651
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6036841845869984075}
  m_Layer: 6
  m_Name: Particle_Effects
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6036841845869984075
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6696435169527005651}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6691514428465714447}
  - {fileID: 1100487531278119537}
  - {fileID: 5890071723512618041}
  - {fileID: 357639824793656250}
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6749749964942156214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8816832650939081125}
  m_Layer: 0
  m_Name: mixamorig:RightHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8816832650939081125
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6749749964942156214}
  serializedVersion: 2
  m_LocalRotation: {x: 0.2518963, y: -0.05946447, z: 0.22192448, w: 0.94008607}
  m_LocalPosition: {x: -0.03002975, y: 0.03788804, z: 0.021671543}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5006849877934591033}
  m_Father: {fileID: 3979363624826852168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6849111818355561840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 386286488635967096}
  m_Layer: 0
  m_Name: shotgunBarrels
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &386286488635967096
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6849111818355561840}
  serializedVersion: 2
  m_LocalRotation: {x: 0.66399896, y: 0.000000025610149, z: 0.00000047614125, w: 0.74773353}
  m_LocalPosition: {x: 6.964456e-15, y: -0.00011094182, z: 0.0011043664}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2069568807697715976}
  m_Father: {fileID: 3131700382896726967}
  m_LocalEulerAnglesHint: {x: 83.211, y: 0, z: 0}
--- !u!1 &6861148586072546693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6182511211354245451}
  - component: {fileID: 5452676094076374343}
  m_Layer: 0
  m_Name: Brick
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6182511211354245451
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6861148586072546693}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8041038960164555037}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5452676094076374343
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6861148586072546693}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 2
  itemVisual: {fileID: 7267775124430853031}
--- !u!1 &6911958321989881364
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2900324770955326982}
  m_Layer: 0
  m_Name: mixamorig:LeftHandMiddle4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2900324770955326982
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6911958321989881364}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.03680194, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2269580635770912648}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7014909145387098111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1380415997891719551}
  m_Layer: 0
  m_Name: mixamorig:LeftHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1380415997891719551
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7014909145387098111}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000016729259, y: 0.000000014193132, z: 0.00000016846676, w: 1}
  m_LocalPosition: {x: 0.0000000017706406, y: 0.038919788, z: -0.000000026128674}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3975215770376766830}
  m_Father: {fileID: 4781822877642270152}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7095738091165812422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1072571140952975907}
  - component: {fileID: 3964752323570111147}
  - component: {fileID: 8537437682833549768}
  m_Layer: 0
  m_Name: shells
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1072571140952975907
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7095738091165812422}
  serializedVersion: 2
  m_LocalRotation: {x: 0.91597563, y: -0.12059048, z: -0.0499502, w: -0.37940952}
  m_LocalPosition: {x: -0.00021914659, y: 0.085669585, z: 0.068817504}
  m_LocalScale: {x: 0.80000013, y: 0.80000013, z: 0.80000013}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8809163776382285054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3964752323570111147
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7095738091165812422}
  m_Mesh: {fileID: 3297411833427862167, guid: 0faba8f2d76279e40b4e279bf82aa54c, type: 3}
--- !u!23 &8537437682833549768
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7095738091165812422}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6ce9dc103f4107b45ad1a866ce0e197f, type: 2}
  - {fileID: 2100000, guid: 14c2d7e277d4f9d4590795b86bd982be, type: 2}
  - {fileID: 2100000, guid: 83c24d5547e3e9f4382e262a4b8781bf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7178666120901570473
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4131724537613149640}
  m_Layer: 0
  m_Name: mixamorig:RightHandIndex4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4131724537613149640
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7178666120901570473}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00007257524, y: 0.0067092534, z: -0.0013747222, w: 0.9999766}
  m_LocalPosition: {x: 0.0000000010521762, y: 0.030779975, z: -0.00000011061593}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8088835260423587755}
  m_Father: {fileID: 1196209538880856154}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7243968816475140791
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1184128333235670976}
  m_Layer: 0
  m_Name: shotgunTrigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1184128333235670976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7243968816475140791}
  serializedVersion: 2
  m_LocalRotation: {x: 0.97572595, y: -0.000000026106243, z: -0.0000001163156, w: 0.21899515}
  m_LocalPosition: {x: 4.3242964e-20, y: -0.00019474901, z: 0.000014791062}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7502453955486831123}
  m_Father: {fileID: 6113962709007682328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7252838276702911586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5662694111764699321}
  m_Layer: 0
  m_Name: Bone.006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5662694111764699321
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7252838276702911586}
  serializedVersion: 2
  m_LocalRotation: {x: 3.691742e-12, y: 0.000000008370876, z: -0.0000000030267984, w: 1}
  m_LocalPosition: {x: -4.6566128e-12, y: 0.005075134, z: -7.734524e-12}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1232300103819153417}
  m_Father: {fileID: 8256387542372928475}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7267775124430853031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8041038960164555037}
  - component: {fileID: 6356929563718600376}
  - component: {fileID: 9213430837128770162}
  m_Layer: 0
  m_Name: Brick_Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &8041038960164555037
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7267775124430853031}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6532815, y: -0.6532815, z: 0.27059805, w: 0.27059805}
  m_LocalPosition: {x: 0, y: 0.03, z: -0.16394049}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6182511211354245451}
  m_LocalEulerAnglesHint: {x: 135, y: 0, z: 90}
--- !u!33 &6356929563718600376
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7267775124430853031}
  m_Mesh: {fileID: -7018330538815869774, guid: dbc744ebd221cdb4ba4460443c7817dd, type: 3}
--- !u!23 &9213430837128770162
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7267775124430853031}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: da4164ee68b0c0c4095c70bfbf68b492, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7325701845219264823
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3051096137945382918}
  m_Layer: 0
  m_Name: mixamorig:RightFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3051096137945382918
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7325701845219264823}
  serializedVersion: 2
  m_LocalRotation: {x: 0.58325434, y: 0.028363375, z: 0.042650275, w: 0.8106731}
  m_LocalPosition: {x: 0.000000006370246, y: 0.43157488, z: 0.0000000021466986}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2764264087436889369}
  m_Father: {fileID: 4683132479299004705}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7379540814301942276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7817572369376365240}
  m_Layer: 0
  m_Name: Bone.007_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7817572369376365240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7379540814301942276}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0055978885, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1306324168190396528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7393060224217739596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7632165182453806318}
  m_Layer: 0
  m_Name: mixamorig:RightHandThumb4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7632165182453806318
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7393060224217739596}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009232318, y: 0.12713102, z: 0.07232819, w: 0.98920226}
  m_LocalPosition: {x: -0.00000011563301, y: 0.034590743, z: -0.00000027511277}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3828028002783979645}
  m_Father: {fileID: 3823537968796596275}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7413757174815395100
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3823537968796596275}
  m_Layer: 0
  m_Name: mixamorig:RightHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3823537968796596275
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7413757174815395100}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000002581543, y: -0.00000065786776, z: -0.00000033527618, w: 1}
  m_LocalPosition: {x: 0.000000041127205, y: 0.043821424, z: -0.000000115938086}
  m_LocalScale: {x: 0.99999994, y: 0.99999976, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7632165182453806318}
  m_Father: {fileID: 5006849877934591033}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7418903900296664994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2749552526999073506}
  - component: {fileID: 1063392220777832571}
  - component: {fileID: 165632422780067095}
  m_Layer: 0
  m_Name: TicketVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2749552526999073506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7418903900296664994}
  serializedVersion: 2
  m_LocalRotation: {x: 0.61298853, y: 0.3524841, z: 0.61298853, w: 0.3524841}
  m_LocalPosition: {x: 0, y: 0, z: -0.16394046}
  m_LocalScale: {x: 0.2, y: 0.001, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3483370276486656611}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 120.2}
--- !u!33 &1063392220777832571
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7418903900296664994}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &165632422780067095
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7418903900296664994}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e726875c60e804344a4ae9ef1ebbb0ed, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7485346959753412790
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7243968780005644107}
  - component: {fileID: 5014269307766458858}
  m_Layer: 0
  m_Name: Shotgun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7243968780005644107
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7485346959753412790}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1477125319424636728}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5014269307766458858
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7485346959753412790}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 15
  itemVisual: {fileID: 3748827240111302516}
--- !u!1 &7486886810415973883
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2693815886802083000}
  m_Layer: 0
  m_Name: mixamorig:LeftShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2693815886802083000
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7486886810415973883}
  serializedVersion: 2
  m_LocalRotation: {x: 0.453869, y: -0.544821, z: 0.55116665, w: 0.43975943}
  m_LocalPosition: {x: -0.06105825, y: 0.09106284, z: 0.0075706276}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 492023801351764627}
  m_Father: {fileID: 3945256988625133481}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7521596625947814960
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 492023801351764627}
  m_Layer: 0
  m_Name: mixamorig:LeftArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &492023801351764627
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7521596625947814960}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010471312, y: 0.0010649258, z: -0.1011494, w: 0.9948156}
  m_LocalPosition: {x: 0.0000000035762786, y: 0.12922287, z: 0.00000007166605}
  m_LocalScale: {x: 0.99999994, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 991462354720707243}
  - {fileID: 1365167465497023489}
  m_Father: {fileID: 2693815886802083000}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7698468112273716447
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 993087210239133413}
  m_Layer: 0
  m_Name: mixamorig:LeftHandPinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &993087210239133413
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7698468112273716447}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016043273, y: 0.00000035354583, z: -0.00000002319739, w: 1}
  m_LocalPosition: {x: -0.0000000019737536, y: 0.0413666, z: 0.000000025797315}
  m_LocalScale: {x: 0.9999998, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5377745395822255802}
  m_Father: {fileID: ************1408069}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7812586951838351475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3834324505224384244}
  - component: {fileID: 3297568450549869079}
  - component: {fileID: 6370244926997209924}
  - component: {fileID: 7289181853176218816}
  - component: {fileID: 8282734940496870642}
  - component: {fileID: 3343799980989206046}
  - component: {fileID: 5744676234014132664}
  - component: {fileID: 7259423219028248817}
  - component: {fileID: -9195698824272580052}
  - component: {fileID: 4839163721224306418}
  - component: {fileID: 6510411109061613489}
  - component: {fileID: 5092658939758589807}
  m_Layer: 6
  m_Name: PlayerCharacter
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3834324505224384244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5072942232072495766}
  - {fileID: 7397464212288208869}
  - {fileID: 1637936185260825465}
  - {fileID: 516958559413986497}
  - {fileID: 1281982887216609419}
  - {fileID: 1739786462606985517}
  - {fileID: 6036841845869984075}
  - {fileID: 15628321936966587}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3297568450549869079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1552182283, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SortKey: 3617898819
  ObjectInterest: 1
  Flags: 262145
  NestedObjects: []
  NetworkedBehaviours:
  - {fileID: 7289181853176218816}
  - {fileID: 8282734940496870642}
  - {fileID: 3343799980989206046}
  - {fileID: 5744676234014132664}
  - {fileID: 7259423219028248817}
  - {fileID: -9195698824272580052}
  - {fileID: 4839163721224306418}
  - {fileID: 6510411109061613489}
  - {fileID: 1910258529398982482}
  ForceRemoteRenderTimeframe: 0
--- !u!54 &6370244926997209924
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 126
  m_CollisionDetection: 2
--- !u!114 &7289181853176218816
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c91469fbd141a6245aece7d6ef974afe, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  KCC: {fileID: 3343799980989206046}
  health: {fileID: 5744676234014132664}
  animator: {fileID: 5128096326010086129}
  moveSpeed: 3.5
  sprintMultiplier: 1.4
  jumpForce: 6
  useRange: 4
  jumpSound: {fileID: 5761633887109336059}
  jumpClips:
  - {fileID: 8300000, guid: 0cb6522195d655044b3ed338b4e25d08, type: 3}
  - {fileID: 8300000, guid: c5256a37f387d6d4c9662f57212e91a3, type: 3}
  - {fileID: 8300000, guid: 64ebb490022e14145b0c6705a994b12f, type: 3}
  cameraHandle: {fileID: 5072942232072495766}
  thirdPersonRoot: {fileID: 1909900111695880753}
  firstPersonItemHolder: {fileID: 447037791068707182}
  thirdPersonItemHolder: {fileID: 7904184815933716786}
  laserBeamSource: {fileID: 3710916564237207501}
  laserBeamFX1: {fileID: 4521568328032124891}
  laserBeamFX2: {fileID: 4521568328032124891}
  leaderIndicator: {fileID: 8118445705599890211}
  blindfoldAnchor: {fileID: 0}
  teamMaterialRenderer: {fileID: 8396210338827519755}
  shotgunFx: {fileID: 8896170167642888817}
  duckMask: {fileID: 4199784822379677035}
  pickupRadius: 0.25
  pickupRange: 2
  maxPickupRange: 2
  upGravity: 15
  downGravity: 25
  groundAcceleration: 55
  groundDeceleration: 25
  airAcceleration: 25
  airDeceleration: 1.3
  _CurrentItem: {fileID: 0}
  _IsLeader: 0
  _TeamId: 0
  _IsFrozen: 0
  _IsMovementLocked: 0
  _IsDancing: 0
  _CurrentHat: {fileID: 0}
  _IsClimbing: 0
  _CurrentHold: {fileID: 0}
--- !u!114 &8282734940496870642
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f5edcb8eb1153a24c81c22d608645779, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &3343799980989206046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1869825851, guid: 977dbcf975465374990f471e1497f563, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _stateAuthorityChangeErrorCorrectionDelta: 0
  _settings:
    Shape: 1
    IsTrigger: 0
    Radius: 0.2
    Height: 1.8
    Extent: 0.035
    ColliderLayer: 7
    CollisionLayerMask:
      serializedVersion: 2
      m_Bits: 230529
    ProxyInterpolationMode: 0
    TeleportThreshold: 1
    AntiJitterDistance: {x: 0.025, y: 0.01}
    CompressNetworkPosition: 0
    ForcePredictedLookRotation: 0
    StepHeight: 0.5
    StepDepth: 0.2
    StepSpeed: 1
    StepMinPushBack: 0.5
    StepGroundCheckRadiusScale: 0.5
    StepRequireGroundTarget: 0
    SnapDistance: 0.25
    SnapSpeed: 4
--- !u!114 &5744676234014132664
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13e08f7ea5af4b80907f2f169a88f45a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MaxHealth: 100
  ImmortalDurationAfterSpawn: 2
  ImmortalityIndicator: {fileID: 3298037477409314037, guid: 4f3f8ee7fc52a90478fa9bd1ceb1f55e, type: 3}
  _CurrentHealth: 0
--- !u!114 &7259423219028248817
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cd0c684449444d0496b61a8c0dbb43bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  KCC: {fileID: 3343799980989206046}
  FootstepClips:
  - {fileID: 8300000, guid: d7aa67db40121f0468bf2f1ff8203518, type: 3}
  - {fileID: 8300000, guid: 6a06ead78049498419565aa18134ab57, type: 3}
  - {fileID: 8300000, guid: fc89ceb1a1234dc4588f53f8f2428b9a, type: 3}
  - {fileID: 8300000, guid: b1bb4881b8d1bab41810c51daf8ac98f, type: 3}
  - {fileID: 8300000, guid: 638143a6f128a0a48b2e286731e0004b, type: 3}
  - {fileID: 8300000, guid: e747c49443301f4468ef43f662a77c68, type: 3}
  - {fileID: 8300000, guid: 2a5c21b7f1db2664ca4ebb2b598a5e94, type: 3}
  FootstepSource: {fileID: 9195169381146776712}
  FootstepDuration: 0.3
--- !u!114 &-9195698824272580052
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 05d5c1eb9d2c80046a66a461c7f28624, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &4839163721224306418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1039544458, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Config: 3
  BroadRadius: 1.8412858
  Offset: {x: 0, y: 0, z: 0}
  GizmosColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  Hitboxes:
  - {fileID: 2131800937902847223}
  - {fileID: 4064681035785975638}
  - {fileID: 2387298400630587860}
  - {fileID: 8950084132834479254}
  - {fileID: 2186094072346708344}
  - {fileID: 1780460002125149266}
--- !u!114 &6510411109061613489
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e03e725369e38e64f93ee74e4e02d317, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playerChunkFx: {fileID: 1104469487782874905}
  bloodWallFx: {fileID: 4584196884900185907}
  chunkFx: {fileID: 6686546214324004967}
  bloodSplashFx: {fileID: 5834264008093086975}
  deathSound: {fileID: 8300000, guid: f4d887f228a05bb47bb6cc4e211471f5, type: 3}
  deathAudioSource: {fileID: 5916202116017335851}
--- !u!114 &5092658939758589807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7812586951838351475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 18da7eb746183da46b9cae9b52cf443d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nameText: {fileID: 2757435449578730600}
  defaultNameColor: {r: 1, g: 1, b: 1, a: 1}
  leaderNameColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
  speakingColor: {r: 0, g: 1, b: 0, a: 1}
  mutedColor: {r: 1, g: 0, b: 0, a: 1}
  showDistance: 10
--- !u!1 &7866703777051741191
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9101375329063990286}
  m_Layer: 0
  m_Name: mixamorig:RightHandPinky4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9101375329063990286
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7866703777051741191}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.029238489, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1963095064706926255}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7897549327193858620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7113456155712932134}
  m_Layer: 0
  m_Name: mixamorig:LeftToe_End_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7113456155712932134
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7897549327193858620}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.09999603, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4236546463364950899}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7926129645994128689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4929543403243503095}
  m_Layer: 0
  m_Name: mixamorig:RightShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4929543403243503095
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7926129645994128689}
  serializedVersion: 2
  m_LocalRotation: {x: -0.45380348, y: -0.5448757, z: 0.55111223, w: -0.43982756}
  m_LocalPosition: {x: 0.06105696, y: 0.09106372, z: 0.00757076}
  m_LocalScale: {x: 1, y: 1.0000002, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3015082048921422199}
  m_Father: {fileID: 3945256988625133481}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8005033872583672729
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2481288568660384567}
  - component: {fileID: 6587650131733606756}
  - component: {fileID: 1072430432664173500}
  m_Layer: 0
  m_Name: Icosphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2481288568660384567
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8005033872583672729}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 473561278133575726}
  - {fileID: 3875459241672687767}
  - {fileID: 7869059498303244213}
  - {fileID: 1539293413828028038}
  - {fileID: 3084967074228336637}
  - {fileID: 8058059625419994929}
  - {fileID: 5628067510458253910}
  - {fileID: 4682305932347623810}
  - {fileID: 1637557678266448222}
  - {fileID: 6626120662396188838}
  - {fileID: 4694018579658672886}
  - {fileID: 7271550270425546183}
  m_Father: {fileID: 2141234006331023539}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6587650131733606756
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8005033872583672729}
  m_Mesh: {fileID: 289301614986012343, guid: 49242180ad10897428c1e5bce35b75a0, type: 3}
--- !u!23 &1072430432664173500
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8005033872583672729}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: dac09a1388d4ad441b1d07ac57215739, type: 2}
  - {fileID: 2100000, guid: 7ca5a7479aa12134aa6f032e88fe3511, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8048319736418767425
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4643394305985780944}
  m_Layer: 0
  m_Name: yoyo_cup
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4643394305985780944
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8048319736418767425}
  serializedVersion: 2
  m_LocalRotation: {x: -0.59541845, y: -0.38141447, z: 0.38141438, w: 0.59541833}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100.00001, y: 100, z: 100.00001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6758121775632514030}
  - {fileID: 3303035721439681923}
  m_Father: {fileID: 5555431556190608973}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8074717715789374683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5486752880079072316}
  m_Layer: 0
  m_Name: mixamorig:RightHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5486752880079072316
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8074717715789374683}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000007949973, y: 0.00000007981751, z: 0.0000004745715, w: 1}
  m_LocalPosition: {x: -0.028220426, y: 0.1226661, z: 0.0023183736}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7133777301257100208}
  m_Father: {fileID: 3979363624826852168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8118445705599890211
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 516958559413986497}
  - component: {fileID: 1135774027481591884}
  - component: {fileID: 3787610211686474909}
  m_Layer: 0
  m_Name: LeaderIndicator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &516958559413986497
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8118445705599890211}
  serializedVersion: 2
  m_LocalRotation: {x: 0.1464466, y: 0.35355338, z: 0.35355338, w: 0.8535535}
  m_LocalPosition: {x: 0, y: 1.944, z: 0}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 45, z: 45}
--- !u!33 &1135774027481591884
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8118445705599890211}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3787610211686474909
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8118445705599890211}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 92f2634cd91d6ae40ab19a02171189aa, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8169286849145927954
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1637936185260825465}
  - component: {fileID: 5761633887109336059}
  m_Layer: 6
  m_Name: PlayerSound
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1637936185260825465
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8169286849145927954}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.59, z: 0.573}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3834324505224384244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &5761633887109336059
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8169286849145927954}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: -973724649278464913, guid: bfbc8b07bd216478eb5fca35a6fe51e2, type: 2}
  m_audioClip: {fileID: 8300000, guid: 0cb6522195d655044b3ed338b4e25d08, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 0
  MinDistance: 3
  MaxDistance: 50
  Pan2D: 0
  rolloffMode: 2
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0.03
      value: 1
      inSlope: -33.346664
      outSlope: -33.346664
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.06
      value: 0.46783447
      inSlope: -8.336666
      outSlope: -8.336666
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.12
      value: 0.21783447
      inSlope: -2.0841665
      outSlope: -2.0841665
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.24
      value: 0.09283447
      inSlope: -0.52104163
      outSlope: -0.52104163
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.48
      value: 0.030334473
      inSlope: -0.13026041
      outSlope: -0.13026041
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.96
      value: 0
      inSlope: -0.032565102
      outSlope: -0.032565102
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: -0.030011963
      outSlope: -0.030011963
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.41666666
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &8194888292153290219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7536925568937949801}
  m_Layer: 0
  m_Name: mixamorig:Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7536925568937949801
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8194888292153290219}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0028276602, y: -1.786553e-12, z: 2.95366e-11, w: 0.999996}
  m_LocalPosition: {x: -0.00000025480355, y: 0.15027755, z: 0.008779066}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8235076030647147179}
  m_Father: {fileID: 3945256988625133481}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8232679573011859075
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4192918227577770735}
  m_Layer: 0
  m_Name: mixamorig:RightHandPinky3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4192918227577770735
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8232679573011859075}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000001960186, y: -0.000000022925416, z: 0.000000039285567, w: 1}
  m_LocalPosition: {x: 0.0000000054604463, y: 0.025948502, z: -0.000000055303907}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1963095064706926255}
  m_Father: {fileID: 1900327639670871439}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8234557564679572138
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3817182319982510986}
  m_Layer: 0
  m_Name: yoyo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3817182319982510986
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8234557564679572138}
  serializedVersion: 2
  m_LocalRotation: {x: -0.6251111, y: 0.33050886, z: 0.33050886, w: 0.6251111}
  m_LocalPosition: {x: -6.774242e-11, y: -0.0000938229, z: 1.07920166e-10}
  m_LocalScale: {x: 1, y: 1.4374262, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8256387542372928475}
  - {fileID: 1999178560297187524}
  - {fileID: 8775210194547003685}
  - {fileID: 1306324168190396528}
  m_Father: {fileID: 4898546221169652666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8267065589778353768
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4326703914208961461}
  - component: {fileID: 1768059147116863066}
  - component: {fileID: 6736895112914663544}
  m_Layer: 0
  m_Name: eye_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4326703914208961461
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8267065589778353768}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0.1, z: 0.2}
  m_LocalScale: {x: 0.1, y: 0.2, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6368492073700593264}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1768059147116863066
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8267065589778353768}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6736895112914663544
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8267065589778353768}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 089df2b5e9699af40bdcaad42291d420, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8273439069596642789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7639872423608008687}
  m_Layer: 0
  m_Name: mixamorig:RightHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7639872423608008687
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8273439069596642789}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000001696122, y: -0.00000009617503, z: -0.00000015577635, w: 1}
  m_LocalPosition: {x: -0.00000000733753, y: 0.03613975, z: -0.000000012286407}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7861715622442640672}
  m_Father: {fileID: 6589849949144326397}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8319565664659431275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4795485296645381212}
  m_Layer: 0
  m_Name: mixamorig:LeftToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4795485296645381212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8319565664659431275}
  serializedVersion: 2
  m_LocalRotation: {x: 0.22763655, y: 0.032281276, z: 0.011236449, w: 0.9731461}
  m_LocalPosition: {x: 0.0000000043585895, y: 0.15719804, z: -0.000000001490116}
  m_LocalScale: {x: 0.99999994, y: 0.9999998, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4236546463364950899}
  m_Father: {fileID: 7999865686142132600}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8361237561072049239
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7642223773478067609}
  m_Layer: 0
  m_Name: mixamorig:LeftHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7642223773478067609
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8361237561072049239}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000007422003, y: 0.0000000057141847, z: 0.00000019930303, w: 1}
  m_LocalPosition: {x: -0.000000037550926, y: 0.043821342, z: 0.00000012464639}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8335864589201569616}
  m_Father: {fileID: 2497612934451456764}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8361926004170490854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7623510548840571555}
  m_Layer: 0
  m_Name: Bone.003_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7623510548840571555
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8361926004170490854}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0065895137, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2356688143703533270}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8476793588518298913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7946224408460213750}
  m_Layer: 0
  m_Name: mixamorig:RightHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7946224408460213750
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8476793588518298913}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000007497206, y: 0.000000013270867, z: -0.0000000690425, w: 1}
  m_LocalPosition: {x: 0.0000000024206992, y: 0.03601198, z: -0.0000000797518}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6021729432587539947}
  m_Father: {fileID: 8443419981025829252}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8531335954666087977
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4682305932347623810}
  - component: {fileID: 6808443250469023783}
  - component: {fileID: 6807280509750195045}
  m_Layer: 0
  m_Name: Circle.001 (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4682305932347623810
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8531335954666087977}
  serializedVersion: 2
  m_LocalRotation: {x: -0.22582795, y: 0.8186727, z: -0.52677506, w: 0.035844978}
  m_LocalPosition: {x: 0.1377, y: -0.38, z: -0.213}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2481288568660384567}
  m_LocalEulerAnglesHint: {x: 57.814, y: -213.838, z: -49.912}
--- !u!33 &6808443250469023783
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8531335954666087977}
  m_Mesh: {fileID: 8314571269056883277, guid: 4371c593aa4b1d643ba2c06cf2c1bda3, type: 3}
--- !u!23 &6807280509750195045
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8531335954666087977}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c9b04a9f948393a4ea745d9c18d8c12d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8559853074373037124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6008282678656264706}
  - component: {fileID: 8084409922234097107}
  m_Layer: 0
  m_Name: Laser
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6008282678656264706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8559853074373037124}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3710916564237207501}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8084409922234097107
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8559853074373037124}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 5
  itemVisual: {fileID: 2305107505980451479}
--- !u!1 &8614040992464163982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2933040711103675446}
  m_Layer: 0
  m_Name: mixamorig:RightHandRing4_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2933040711103675446
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8614040992464163982}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.03660111, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8462894559293616617}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8639482591129395807
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7888707815337306133}
  m_Layer: 0
  m_Name: Armature
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7888707815337306133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8639482591129395807}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000037748947, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2812900929563455671}
  m_Father: {fileID: 1281982887216609419}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8748345989236028690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8328102573727064540}
  - component: {fileID: 7922992959988475344}
  m_Layer: 0
  m_Name: Knife
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8328102573727064540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8748345989236028690}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5640246200841618342}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7922992959988475344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8748345989236028690}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 4
  itemVisual: {fileID: 4975913151499933980}
--- !u!1 &8778992931062609863
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1963095064706926255}
  m_Layer: 0
  m_Name: mixamorig:RightHandPinky4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1963095064706926255
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8778992931062609863}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00008301745, y: 0.008498835, z: -0.0022061947, w: 0.9999615}
  m_LocalPosition: {x: -8.5318785e-10, y: 0.029238872, z: 0.000000020867082}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9101375329063990286}
  m_Father: {fileID: 4192918227577770735}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8918607035701690491
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3945256988625133481}
  m_Layer: 0
  m_Name: mixamorig:Spine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3945256988625133481
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8918607035701690491}
  serializedVersion: 2
  m_LocalRotation: {x: 0.057711728, y: -0.0000021958208, z: -0.000003285133, w: 0.9983333}
  m_LocalPosition: {x: -1.5461409e-12, y: 0.13458836, z: 0.000000009871396}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2693815886802083000}
  - {fileID: 7536925568937949801}
  - {fileID: 4929543403243503095}
  m_Father: {fileID: 8856331593397087652}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8927441178296491196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4775554780989781846}
  m_Layer: 0
  m_Name: mixamorig:RightHandMiddle4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4775554780989781846
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8927441178296491196}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000050827464, y: 0.0066952943, z: 0.0017083231, w: 0.99997616}
  m_LocalPosition: {x: -0.00000028999864, y: 0.03680198, z: 0.000000020340483}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5912451314015105652}
  m_Father: {fileID: 7861715622442640672}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9004793588640636111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2356688143703533270}
  m_Layer: 0
  m_Name: Bone.003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2356688143703533270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9004793588640636111}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000058203313, y: 0.15554608, z: -0.000000006353252, w: 0.9878287}
  m_LocalPosition: {x: 1.2934187e-12, y: 0.005054093, z: 5.1656457e-14}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7623510548840571555}
  m_Father: {fileID: 6477283916791895790}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9056948391547330738
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1335128677452947767}
  - component: {fileID: 5775769135599441438}
  - component: {fileID: 2703266824538675441}
  m_Layer: 0
  m_Name: BoomFPP
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1335128677452947767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9056948391547330738}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: 0, y: 0, z: -0.063904315}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1969129186948030317}
  m_LocalEulerAnglesHint: {x: 270, y: 0, z: 270}
--- !u!33 &5775769135599441438
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9056948391547330738}
  m_Mesh: {fileID: -1595160273072348756, guid: e5baf2215147dce41810f3bc4568e69d, type: 3}
--- !u!23 &2703266824538675441
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9056948391547330738}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b328842212df3b64c9b9f5e962b073f8, type: 2}
  - {fileID: 2100000, guid: 1a78d194aaf7e6b47be1d63ac2628d74, type: 2}
  - {fileID: 2100000, guid: 84138643536c9b64ebd48ed800b5b6f4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &9093220667411660831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4208488758885925859}
  - component: {fileID: 8700320096658921720}
  m_Layer: 0
  m_Name: Cap
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4208488758885925859
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9093220667411660831}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 501319966028415895}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8700320096658921720
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9093220667411660831}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 8
  itemVisual: {fileID: 13982166261312329}
--- !u!1 &9097110401983451379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3979363624826852168}
  m_Layer: 0
  m_Name: mixamorig:RightHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3979363624826852168
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9097110401983451379}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023674225, y: 0.000000176434, z: 0.000000116829526, w: 1}
  m_LocalPosition: {x: -0.000000133475, y: 0.27614462, z: 0.00000017069877}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5486752880079072316}
  - {fileID: 6589849949144326397}
  - {fileID: 1904523340395724154}
  - {fileID: 8443419981025829252}
  - {fileID: 8816832650939081125}
  - {fileID: 7904184815933716786}
  m_Father: {fileID: 3540884956922506229}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9122358101884199299
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3597526475082932922}
  - component: {fileID: 8560745903009348777}
  m_Layer: 0
  m_Name: DuckItem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3597526475082932922
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9122358101884199299}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 40247463152598488}
  m_Father: {fileID: 447037791068707182}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8560745903009348777
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9122358101884199299}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef989d669b9a12044bbe70e95a4e2450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemID: 12
  itemVisual: {fileID: 2958823821205566864}
--- !u!1 &9125637641072620654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7666835147254385239}
  - component: {fileID: 8728600580990177289}
  m_Layer: 0
  m_Name: shotgunMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7666835147254385239
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9125637641072620654}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1477125319424636728}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8728600580990177289
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9125637641072620654}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5f07c625847716642a1d2cda26395467, type: 2}
  - {fileID: 2100000, guid: 6247e7591a187944cac07b47d75e3be3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -2578597674188618612, guid: 0faba8f2d76279e40b4e279bf82aa54c, type: 3}
  m_Bones:
  - {fileID: 3131700382896726967}
  - {fileID: 386286488635967096}
  - {fileID: 7105427196862503063}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3131700382896726967}
  m_AABB:
    m_Center: {x: 5.820766e-11, y: -0.0004417888, z: 0.00087714294}
    m_Extent: {x: 0.00034913432, y: 0.001031038, z: 0.0021444745}
  m_DirtyAABB: 0
--- !u!1 &9201071923094070232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8809163776382285054}
  m_Layer: 0
  m_Name: ShotgunVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &8809163776382285054
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9201071923094070232}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0021308032, y: 0.044381633, z: 0.016612733, w: -0.9988743}
  m_LocalPosition: {x: -0.19975476, y: -0.0005272627, z: -0.1752052}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1072571140952975907}
  - {fileID: 7628779179626297188}
  - {fileID: 3402534984475402420}
  - {fileID: 7753838758981653905}
  m_Father: {fileID: 4054486398653610702}
  m_LocalEulerAnglesHint: {x: -0.328, y: 354.917, z: -1.891}
--- !u!1001 &1104887230765678301
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6036841845869984075}
    m_Modifications:
    - target: {fileID: 1834194341294532, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_Name
      value: Particle_PlayerChunk
      objectReference: {fileID: 0}
    - target: {fileID: 1834194341294532, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
--- !u!4 &1100487531278119537 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4765838103219372, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
  m_PrefabInstance: {fileID: 1104887230765678301}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1104469487782874905 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1834194341294532, guid: d5f26854a8904624c8073139cd2eb346, type: 3}
  m_PrefabInstance: {fileID: 1104887230765678301}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2335962335347471675
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8809163776382285054}
    m_Modifications:
    - target: {fileID: 101831492035729737, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_Name
      value: shotgunFx
      objectReference: {fileID: 0}
    - target: {fileID: 101831492035729737, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.018
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.307
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
--- !u!4 &7753838758981653905 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
  m_PrefabInstance: {fileID: 2335962335347471675}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3077842888241448522
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6036841845869984075}
    m_Modifications:
    - target: {fileID: 1524722512136864121, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_Name
      value: Particle_BloodWall
      objectReference: {fileID: 0}
    - target: {fileID: 1524722512136864121, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9502d3906150df54badf3b13310c4120, type: 3}
--- !u!4 &357639824793656250 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3332705824165584368, guid: 9502d3906150df54badf3b13310c4120, type: 3}
  m_PrefabInstance: {fileID: 3077842888241448522}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4584196884900185907 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1524722512136864121, guid: 9502d3906150df54badf3b13310c4120, type: 3}
  m_PrefabInstance: {fileID: 3077842888241448522}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3606045873486322266
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6036841845869984075}
    m_Modifications:
    - target: {fileID: 7132654703251713701, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_Name
      value: Particle_BloodSplash
      objectReference: {fileID: 0}
    - target: {fileID: 7132654703251713701, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
--- !u!1 &5834264008093086975 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7132654703251713701, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
  m_PrefabInstance: {fileID: 3606045873486322266}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &5890071723512618041 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7185073858978809443, guid: 18162e022f8a7f54796b40eb58418083, type: 3}
  m_PrefabInstance: {fileID: 3606045873486322266}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6687254365749287331
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6036841845869984075}
    m_Modifications:
    - target: {fileID: 1834194341294532, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_Name
      value: Particle_Chunk
      objectReference: {fileID: 0}
    - target: {fileID: 1834194341294532, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
--- !u!1 &6686546214324004967 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1834194341294532, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
  m_PrefabInstance: {fileID: 6687254365749287331}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &6691514428465714447 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4765838103219372, guid: 03c2a19800625a547a0d42ad11b8cd20, type: 3}
  m_PrefabInstance: {fileID: 6687254365749287331}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8798991961831031608
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1477125319424636728}
    m_Modifications:
    - target: {fileID: 101831492035729737, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_Name
      value: shotgunFx
      objectReference: {fileID: 0}
    - target: {fileID: 101831492035729737, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.018
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.307
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
--- !u!4 &3597362949140177810 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5471919843207946410, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
  m_PrefabInstance: {fileID: 8798991961831031608}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &8896170167642888817 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 101831492035729737, guid: df014970b8a9b644bb1ba704aaa64630, type: 3}
  m_PrefabInstance: {fileID: 8798991961831031608}
  m_PrefabAsset: {fileID: 0}
